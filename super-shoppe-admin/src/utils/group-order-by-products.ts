import { Order, OrderProduct } from "@ts-types/generated";

export interface GroupedOrder extends Omit<Order, 'products'> {
  products: OrderProduct[];
  group_name: string;
}

/**
 * Groups an order by product group names, creating separate order objects for each group
 * @param order - The original order object
 * @returns Array of grouped orders, each containing products from the same group
 */
export function groupOrderByProducts(order: Order): GroupedOrder[] {
  if (!order.products || order.products.length === 0) {
    return [];
  }

  // Group products by their product_group_name
  const productGroups: Record<string, OrderProduct[]> = {};

  order.products.forEach((product) => {
    // Use the product_group_name field directly from the order product
    const groupName = product.product_group_name || 'ungrouped';
    
    if (!productGroups[groupName]) {
      productGroups[groupName] = [];
    }
    productGroups[groupName].push(product);
  });

  // Create separate order objects for each group
  const groupedOrders: GroupedOrder[] = [];

  Object.entries(productGroups).forEach(([groupName, products]) => {
    // Create a new order object with only products from this group
    const groupedOrder: GroupedOrder = {
      ...order,
      products: products,
      group_name: groupName
    };

    groupedOrders.push(groupedOrder);
  });

  // Sort groups: ungrouped first, then alphabetically
  groupedOrders.sort((a, b) => {
    if (a.group_name === 'ungrouped' && b.group_name !== 'ungrouped') return -1;
    if (a.group_name !== 'ungrouped' && b.group_name === 'ungrouped') return 1;
    return a.group_name.localeCompare(b.group_name);
  });

  return groupedOrders;
}

/**
 * Gets the display name for a group
 * @param groupName - The group name
 * @returns Formatted display name
 */
export function getGroupDisplayName(groupName: string): string {
  return groupName === 'ungrouped' ? 'No Group' : groupName;
}

/**
 * Gets the total quantity for a group of products
 * @param products - Array of order products
 * @returns Total quantity
 */
export function getGroupTotalQuantity(products: OrderProduct[]): number {
  return products.reduce((total, product) => {
    return total + (product.order_quantity || 0);
  }, 0);
}

/**
 * Gets the total value for a group of products
 * @param products - Array of order products
 * @returns Total value
 */
export function getGroupTotalValue(products: OrderProduct[]): number {
  return products.reduce((total, product) => {
    return total + (product.subtotal || 0);
  }, 0);
}
