import {
  Page,
  Text,
  View,
  Document,
  StyleSheet,
  Font,
  Image,
} from "@react-pdf/renderer";
import { siteSettings } from "@settings/site.settings";
import {
  Location,
  Order,
  OrderProduct,
  UserAddress,
} from "@ts-types/generated";
import { formatAddress } from "@utils/format-address";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { parseToFloat } from "@utils/use-price";

interface GroupedProducts {
  [groupName: string]: OrderProduct[];
}

interface PackingSlipPerGroupProps {
  data: Order;
  groupName: string;
  products: OrderProduct[];
}

export function PackingSlipPerGroupPdf({ data, groupName, products }: PackingSlipPerGroupProps) {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  
  return (
    <Page size="A6">
      <View style={styles.container}>
        {/* Title & Logo */}
        <View style={[styles.addressWrapper, { marginBottom: 10 }]}>
          <View style={styles.titleSection}>
            <Text style={[styles.titleText, { marginBottom: 10 }]}>
              Packing Slip
            </Text>
            <Text style={[styles.subtitleText, { marginBottom: 5 }]}>
              Group: {" "}
              <Text style={{ color: "#000000", fontFamily: "Lato Bold" }}>
                {groupName === 'ungrouped' ? 'No Group' : groupName}
              </Text>
            </Text>
            <Text style={[styles.subtitleText]}>
              Shipping Method:{" "}
              <Text style={{ color: "#000000", fontFamily: "Lato Bold" }}>
                {data.shipping_method?.name || 'N/A'}
              </Text>
            </Text>
          </View>

          <View style={[styles.addressTextRight, { marginLeft: "auto" }]}>
            <Image
              src={`${window.location.origin}/logo.png`}
              style={[styles.logo]}
            />
          </View>
        </View>

        {/* Address */}
        <View style={[styles.addressWrapper, { marginBottom: 16 }]}>
          <View style={styles.section}>
            <Text style={[styles.addressText, { marginBottom: 12 }]}>
              Order No:{" "}
              <Text style={{ color: "#000000", fontFamily: "Lato Bold" }}>
                {data.display_id}
              </Text>
            </Text>
            <Text
              style={[
                styles.addressText,
                { color: "#000000", fontFamily: "Lato Bold", fontSize: 9 },
              ]}
            >
              {data?.shipping_address?.recipient}
            </Text>
            <Text style={styles.addressText}>{data?.customer_email}</Text>
            <Text style={styles.addressText}>
              {data?.shipping_address?.contact_number || data?.customer_contact}
            </Text>
            <Text style={styles.addressText}>
              {formatAddress(data?.shipping_address as UserAddress)}
            </Text>
          </View>

          <View style={[styles.section]}>
            <Text style={[styles.addressTextRight, { marginBottom: 20 }]}>
              Order Date:{" "}
              <Text style={{ fontFamily: "Lato Bold" }}>
                {dayjs(data.created_at)
                  .tz(dayjs.tz.guess())
                  .format("D MMMM, YYYY")}
              </Text>
            </Text>
          </View>
        </View>

        {/* Table */}
        <View style={styles.orderTable}>
          <View style={styles.thead}>
            <View style={styles.tr}>
              <Text style={[styles.th, { flex: 1 }]}>Items</Text>
              <Text style={[styles.th, { width: 72, textAlign: "center" }]}>
                SKU
              </Text>
              <Text style={[styles.th, { width: 60, textAlign: "center" }]}>
                Location
              </Text>
              <Text style={[styles.th, { width: 40, textAlign: "right" }]}>
                Units
              </Text>
            </View>
          </View>
          {products.map((item: OrderProduct, index) => {
            const inventory = item.inventory;
            const locations = inventory ? inventory.locations : [];
            const ship_location =
              locations && locations.length > 0
                ? locations.find((loc) => loc.default) ?? locations[0]
                : null;

            return (
              <View style={styles.tbody} key={index}>
                <View style={styles.tr}>
                  <View style={[styles.td, { flex: 1 }]}>
                    <Image
                      src={
                        item.image
                          ? {
                              uri: item.image,
                              method: "GET",
                              headers: {
                                cache: "no-cache",
                                pragma: "no-cache",
                              },
                              body: "",
                            }
                          : `${window.location.origin}${siteSettings.product.placeholder}`
                      }
                      style={[styles.productImage, { marginBottom: 4 }]}
                      cache={!item.image}
                    />
                    <Text
                      style={{ fontFamily: "Lato Bold", color: "#000000" }}
                    >
                      {item.name}
                    </Text>
                  </View>
                  <Text
                    style={[
                      styles.td,
                      {
                        width: 72,
                        textAlign: "center",
                        fontFamily: "Lato Bold",
                        color: "#000000",
                      },
                    ]}
                  >
                    {item.sku}
                  </Text>
                  <View
                    style={[styles.td, { width: 60, textAlign: "center" }]}
                  >
                    {ship_location ? (
                      <>
                        {ship_location.pivot && ship_location.pivot.label ? (
                          <Text
                            style={[
                              {
                                marginBottom: 6,
                                fontFamily: "Lato Bold",
                                color: "#000000",
                              },
                            ]}
                          >
                            {ship_location.pivot.label}
                          </Text>
                        ) : (
                          <></>
                        )}
                        <Text
                          style={{
                            fontFamily: "Lato Bold",
                            color: "#000000",
                          }}
                        >
                          {ship_location.name}
                        </Text>
                      </>
                    ) : (
                      <Text
                        style={{ fontFamily: "Lato Bold", color: "#000000" }}
                      >
                        -
                      </Text>
                    )}
                  </View>
                  <Text
                    style={[
                      styles.td,
                      {
                        width: 40,
                        textAlign: "right",
                        fontFamily: "Lato Bold",
                        color: "#000000",
                      },
                    ]}
                  >
                    {item.order_quantity}
                  </Text>
                </View>
              </View>
            );
          })}
        </View>

        {/* Total */}
        <View style={styles.totalCountWrapper}>
          <View style={styles.totalCountRow}>
            <Text
              style={[
                styles.totalCountCell,
                { fontSize: 9, fontFamily: "Lato Bold", color: "#000000" },
              ]}
            >
              Total
            </Text>
            <Text
              style={[
                styles.totalCountCell,
                { fontSize: 9, fontFamily: "Lato Bold", color: "#000000" },
              ]}
            >
              {products
                .reduce(
                  (p: number, c: OrderProduct) =>
                    p + parseToFloat(c.order_quantity!),
                  0
                )
                .toFixed(0)}
            </Text>
          </View>
        </View>
      </View>
    </Page>
  );
}

export default function PackingSlipPerGroupDocument({ data }: { data: Order }) {
  // Group products by their group name
  const groupedProducts: GroupedProducts = {};
  
  data.products?.forEach((product) => {
    // Get product group from meta data (this would need to be added to the Order type)
    const groupName = (product as any).meta?.product_group_name || 'ungrouped';
    
    if (!groupedProducts[groupName]) {
      groupedProducts[groupName] = [];
    }
    groupedProducts[groupName].push(product);
  });

  return (
    <Document>
      {Object.entries(groupedProducts).map(([groupName, products]) => (
        <PackingSlipPerGroupPdf
          key={groupName}
          data={data}
          groupName={groupName}
          products={products}
        />
      ))}
    </Document>
  );
}

// Register fonts (same as original)
Font.register({
  family: "Lato",
  src: `https://fonts.gstatic.com/s/lato/v16/S6uyw4BMUTPHjx4wWw.ttf`,
});

Font.register({
  family: "Lato Bold",
  src: `https://fonts.gstatic.com/s/lato/v16/S6u9w4BMUTPHh6UVSwiPHA.ttf`,
});

// Styles (same as original)
const styles = StyleSheet.create({
  container: {
    maxWidth: 400,
    flex: 1,
    margin: "10pt",
    fontFamily: "Lato Bold",
  },
  addressWrapper: {
    display: "flex",
    width: "100%",
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  titleSection: {
    width: "60%",
    display: "flex",
    flexDirection: "column",
  },
  section: {
    width: "40%",
    display: "flex",
    flexDirection: "column",
  },
  titleText: {
    fontSize: 12,
    color: "#000000",
    fontFamily: "Lato Bold",
    marginBottom: 5,
  },
  subtitleText: {
    fontSize: 10,
    color: "#000000",
    fontFamily: "Lato Bold",
    marginBottom: 5,
  },
  addressText: {
    fontSize: 8,
    color: "#000000",
    fontFamily: "Lato Bold",
  },
  addressTextRight: {
    fontSize: 8,
    color: "#000000",
    fontFamily: "Lato Bold",
    textAlign: "right",
  },
  orderTable: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
  },
  thead: {
    width: "100%",
    backgroundColor: "#F3F4F6",
    display: "flex",
    flexDirection: "column",
  },
  th: {
    fontSize: 8,
    fontFamily: "Lato Bold",
    color: "#000000",
    padding: "6pt 12pt",
    borderRightWidth: 1,
    borderRightColor: "#ffffff",
    borderRightStyle: "solid",
  },
  tbody: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
  },
  tr: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
  },
  td: {
    fontSize: 7,
    color: "#000000",
    fontFamily: "Lato Bold",
    padding: "6pt 12pt",
    borderTopWidth: 1,
    borderTopColor: "#F3F4F6",
    borderTopStyle: "solid",
    borderRightWidth: 1,
    borderRightColor: "#ffffff",
    borderRightStyle: "solid",
  },
  logo: {
    width: "64px",
    textAlign: "right",
    marginBottom: 2,
  },
  productImage: {
    width: "32px",
    height: "32px",
    textAlign: "left",
    marginRight: 2,
  },
  totalCountWrapper: {
    width: "25%",
    display: "flex",
    flexDirection: "column",
    marginLeft: "auto",
    borderTopWidth: 1,
    borderTopColor: "#000000",
    borderTopStyle: "solid",
  },
  totalCountRow: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  totalCountCell: {
    fontSize: 8,
    color: "#000000",
    fontFamily: "Lato Bold",
    padding: "8pt 16pt 2pt",
  },
});
