import { Download } from "@components/icons/download-icon";
import PackingSlipPdf from "./packing-slip-pdf";
import Button from "@components/ui/button";
import { PDFDownloadLink, PDFViewer, pdf } from "@react-pdf/renderer";
import { Order } from "@ts-types/generated";
import { RefObject, useRef, useState, useEffect } from "react";
import { ImPrinter } from "react-icons/im";
import { mergePdfs } from "@utils/pdf-merge";
import { fetchOrderDelyvaLabel } from "@data/order/use-order-delyva-label.query";
import Loader from "@components/ui/loader/loader";
import cn from "classnames";

type ViewMode = "packing-slip" | "label" | "merged";

const PackingSlipDocument = ({ order }: { order: Order }) => {
  const pdfFrame = useRef() as RefObject<HTMLIFrameElement>;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>("merged");
  const [labelUrl, setLabelUrl] = useState<string | null>(null);
  const [packingSlipBlob, setPackingSlipBlob] = useState<Blob | null>(null);
  const [mergedUrl, setMergedUrl] = useState<string | null>(null);
  const [delyvaLabelUrl, setDelyvaLabelUrl] = useState<string | null>(null);

  useEffect(() => {
    const generatePackingSlipBlob = async () => {
      try {
        const blob = await pdf(<PackingSlipPdf data={order} />).toBlob();
        setPackingSlipBlob(blob);

        // If viewMode is merged, proceed to generate the merged view
        if (viewMode === "merged") {
          setLoading(true);
          try {
            const labelApiUrl = await fetchLabelUrl();
            const mergedPdfBlob = await mergePdfs([labelApiUrl, blob]);
            const newMergedUrl = URL.createObjectURL(mergedPdfBlob);
            setMergedUrl(newMergedUrl);
          } catch (error) {
            console.error("Error generating merged PDF:", error);
            setError("Failed to generate merged PDF");
            // Fall back to label view
            setViewMode("label");
          } finally {
            setLoading(false);
          }
        }
      } catch (error) {
        console.error("Error generating packing slip PDF:", error);
        setError("Failed to generate packing slip PDF");
      }
    };

    generatePackingSlipBlob();
  }, [order]);

  async function handlePrint() {
    if (pdfFrame && pdfFrame.current) {
      if (pdfFrame.current.src) {
        const iframeWindow = pdfFrame.current.contentWindow;
        if (iframeWindow) {
          iframeWindow.focus();
          iframeWindow.print();
        }
      }
    }
  }

  async function fetchLabelUrl() {
    if (delyvaLabelUrl) return delyvaLabelUrl;

    try {
      setLoading(true);
      const { url } = await fetchOrderDelyvaLabel(order?.id?.toString());
      setDelyvaLabelUrl(url);
      return url;
    } catch (error) {
      console.error("Error fetching label URL:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  }

  async function handleViewModeChange(mode: ViewMode) {
    try {
      setLoading(true);
      setError(null);
      setViewMode(mode);

      switch (mode) {
        case "label":
          // Fetch the label if not already fetched
          if (!labelUrl) {
            const labelApiUrl = await fetchLabelUrl();
            setLabelUrl(labelApiUrl);
          }
          break;
        case "merged":
          // Generate merged PDF if not already generated
          if (!mergedUrl && packingSlipBlob) {
            const labelApiUrl = await fetchLabelUrl();
            const mergedPdfBlob = await mergePdfs([
              labelApiUrl,
              packingSlipBlob,
            ]);
            const newMergedUrl = URL.createObjectURL(mergedPdfBlob);
            setMergedUrl(newMergedUrl);
          }
          break;
        default:
          break;
      }
    } catch (error) {
      console.error(`Error changing view to ${mode}:`, error);
      setError(`Failed to display ${mode} view. Please try again.`);
      // Fall back to label view
      setViewMode("label");
    } finally {
      setLoading(false);
    }
  }

  function getActiveTabClass(currentMode: ViewMode) {
    return cn(
      "px-4 py-2 text-sm font-medium rounded-md focus:outline-none",
      viewMode === currentMode
        ? "bg-skin-primary text-white"
        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
    );
  }

  function getCurrentPdfContent() {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-[500px]">
          <Loader text="Loading document..." />
        </div>
      );
    }

    switch (viewMode) {
      case "packing-slip":
        return (
          <PDFViewer
            innerRef={pdfFrame}
            className="w-full h-[1000px]"
            showToolbar={false}
          >
            <PackingSlipPdf data={order} />
          </PDFViewer>
        );
      case "label":
        return labelUrl ? (
          <iframe
            src={labelUrl}
            className="w-full h-[1000px]"
            title="Shipping Label"
            ref={pdfFrame}
          />
        ) : (
          <div className="flex items-center justify-center h-[500px]">
            <p className="text-red-500">Failed to load shipping label</p>
          </div>
        );
      case "merged":
        return mergedUrl ? (
          <iframe
            src={mergedUrl}
            className="w-full h-[1000px]"
            title="Merged Document"
            ref={pdfFrame}
          />
        ) : (
          <div className="flex items-center justify-center h-[500px]">
            <Loader text="Generating combined document..." />
          </div>
        );
      default:
        return null;
    }
  }

  function getDownloadUrl() {
    switch (viewMode) {
      case "label":
        return labelUrl;
      case "merged":
        return mergedUrl;
      default:
        return null;
    }
  }

  function getDownloadFileName() {
    const orderId = order?.display_id || "order";
    switch (viewMode) {
      case "packing-slip":
        return `packing-slip-${orderId}.pdf`;
      case "label":
        return `shipping-label-${orderId}.pdf`;
      case "merged":
        return `complete-document-${orderId}.pdf`;
      default:
        return `document-${orderId}.pdf`;
    }
  }

  return (
    <>
      <div className="w-full flex flex-wrap justify-between items-center border-b p-4 mb-6">
        <div className="flex items-center gap-3 mb-4 md:mb-0">
          {viewMode === "packing-slip" ? (
            <PDFDownloadLink
              document={<PackingSlipPdf data={order} />}
              fileName={getDownloadFileName()}
            >
              {({ loading: downloadLoading }: any) => (
                <Button
                  className="flex items-center gap-0.5"
                  disabled={downloadLoading}
                >
                  <Download className="w-4 h-4" />
                  {downloadLoading ? "Preparing..." : "Download"}
                </Button>
              )}
            </PDFDownloadLink>
          ) : (
            <a
              href={getDownloadUrl() || "#"}
              download={getDownloadFileName()}
              className={
                !getDownloadUrl() ? "pointer-events-none opacity-50" : ""
              }
            >
              <Button
                className="flex items-center gap-0.5"
                disabled={!getDownloadUrl()}
              >
                <Download className="w-4 h-4" />
                Download
              </Button>
            </a>
          )}
          <Button
            className="flex items-center bg-gray-600 hover:bg-gray-700 gap-1"
            onClick={handlePrint}
          >
            <ImPrinter className="w-4 h-4" />
            Print
          </Button>
        </div>
        {error && (
          <div className="text-red-500 text-sm w-full mt-2">{error}</div>
        )}
      </div>

      <div className="px-4 mb-6">
        <div className="flex space-x-2 bg-gray-100 p-1 rounded-lg">
          <button
            className={getActiveTabClass("merged")}
            onClick={() => handleViewModeChange("merged")}
          >
            Combined View
          </button>
          <button
            className={getActiveTabClass("label")}
            onClick={() => handleViewModeChange("label")}
          >
            Shipping Label
          </button>
          <button
            className={getActiveTabClass("packing-slip")}
            onClick={() => handleViewModeChange("packing-slip")}
          >
            Packing Slip
          </button>
        </div>
      </div>

      <div className="p-4">{getCurrentPdfContent()}</div>
    </>
  );
};

export default PackingSlipDocument;
