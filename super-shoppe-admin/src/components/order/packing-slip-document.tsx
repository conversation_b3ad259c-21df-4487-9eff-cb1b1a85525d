import { Download } from "@components/icons/download-icon";
import PackingSlipPdf from "./packing-slip-pdf";
import PackingSlipPerGroupDocument from "./packing-slip-per-group-pdf";
import Button from "@components/ui/button";
import { PDFDownloadLink, PDFViewer, pdf } from "@react-pdf/renderer";
import { Order } from "@ts-types/generated";
import { RefObject, useRef, useState, useEffect } from "react";
import { ImPrinter } from "react-icons/im";
import { mergePdfs } from "@utils/pdf-merge";
import {
  fetchOrderDelyvaLabel,
  fetchOrderDelyvaLabelsPerGroup,
  useOrderDelyvaLabelsPerGroupQuery,
} from "@data/order/use-order-delyva-label.query";
import Loader from "@components/ui/loader/loader";
import cn from "classnames";

type ViewMode =
  | "packing-slip"
  | "packing-slip-groups"
  | "label"
  | "labels-groups"
  | "merged"
  | "merged-groups";

const PackingSlipDocument = ({ order }: { order: Order }) => {
  const pdfFrame = useRef() as RefObject<HTMLIFrameElement>;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>("merged-groups");
  const [labelUrl, setLabelUrl] = useState<string | null>(null);
  const [packingSlipBlob, setPackingSlipBlob] = useState<Blob | null>(null);
  const [packingSlipGroupsBlob, setPackingSlipGroupsBlob] =
    useState<Blob | null>(null);
  const [mergedUrl, setMergedUrl] = useState<string | null>(null);
  const [mergedGroupsUrl, setMergedGroupsUrl] = useState<string | null>(null);
  const [delyvaLabelUrl, setDelyvaLabelUrl] = useState<string | null>(null);
  const [groupLabels, setGroupLabels] = useState<Record<string, any>>({});

  useEffect(() => {
    const generatePackingSlipBlob = async () => {
      try {
        // Generate regular packing slip
        const blob = await pdf(<PackingSlipPdf data={order} />).toBlob();
        setPackingSlipBlob(blob);

        // Generate group-based packing slip
        const groupBlob = await pdf(
          <PackingSlipPerGroupDocument data={order} />
        ).toBlob();
        setPackingSlipGroupsBlob(groupBlob);

        // Fetch group labels
        try {
          const groupLabelsData = await fetchOrderDelyvaLabelsPerGroup(
            order?.id?.toString()
          );
          setGroupLabels(groupLabelsData.groups || {});
        } catch (error) {
          console.error("Error fetching group labels:", error);
          setGroupLabels({});
        }

        // If viewMode is merged-groups, proceed to generate the merged view
        if (viewMode === "merged-groups") {
          setLoading(true);
          try {
            await generateMergedGroupsView(groupBlob);
          } catch (error) {
            console.error("Error generating merged groups PDF:", error);
            setError("Failed to generate merged groups PDF");
            // Fall back to groups view
            setViewMode("packing-slip-groups");
          } finally {
            setLoading(false);
          }
        }
        // If viewMode is merged, proceed to generate the merged view
        else if (viewMode === "merged") {
          setLoading(true);
          try {
            const labelApiUrl = await fetchLabelUrl();
            const mergedPdfBlob = await mergePdfs([labelApiUrl, blob]);
            const newMergedUrl = URL.createObjectURL(mergedPdfBlob);
            setMergedUrl(newMergedUrl);
          } catch (error) {
            console.error("Error generating merged PDF:", error);
            setError("Failed to generate merged PDF");
            // Fall back to label view
            setViewMode("label");
          } finally {
            setLoading(false);
          }
        }
      } catch (error) {
        console.error("Error generating packing slip PDF:", error);
        setError("Failed to generate packing slip PDF");
      }
    };

    generatePackingSlipBlob();
  }, [order]);

  async function handlePrint() {
    if (pdfFrame && pdfFrame.current) {
      if (pdfFrame.current.src) {
        const iframeWindow = pdfFrame.current.contentWindow;
        if (iframeWindow) {
          iframeWindow.focus();
          iframeWindow.print();
        }
      }
    }
  }

  async function fetchLabelUrl() {
    if (delyvaLabelUrl) return delyvaLabelUrl;

    try {
      setLoading(true);
      const { url } = await fetchOrderDelyvaLabel(order?.id?.toString());
      setDelyvaLabelUrl(url);
      return url;
    } catch (error) {
      console.error("Error fetching label URL:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  }

  async function generateMergedGroupsView(groupBlob: Blob) {
    // Get all group label URLs
    const labelUrls: string[] = [];
    for (const [groupName, groupData] of Object.entries(groupLabels)) {
      if (groupData.awb_url) {
        labelUrls.push(groupData.awb_url);
      }
    }

    if (labelUrls.length > 0) {
      // Merge all group labels with the group packing slip
      const mergedPdfBlob = await mergePdfs([...labelUrls, groupBlob]);
      const newMergedUrl = URL.createObjectURL(mergedPdfBlob);
      setMergedGroupsUrl(newMergedUrl);
    } else {
      // No labels available, just use the group packing slip
      const newMergedUrl = URL.createObjectURL(groupBlob);
      setMergedGroupsUrl(newMergedUrl);
    }
  }

  async function handleViewModeChange(mode: ViewMode) {
    try {
      setLoading(true);
      setError(null);
      setViewMode(mode);

      switch (mode) {
        case "label":
          // Fetch the label if not already fetched
          if (!labelUrl) {
            const labelApiUrl = await fetchLabelUrl();
            setLabelUrl(labelApiUrl);
          }
          break;
        case "merged":
          // Generate merged PDF if not already generated
          if (!mergedUrl && packingSlipBlob) {
            const labelApiUrl = await fetchLabelUrl();
            const mergedPdfBlob = await mergePdfs([
              labelApiUrl,
              packingSlipBlob,
            ]);
            const newMergedUrl = URL.createObjectURL(mergedPdfBlob);
            setMergedUrl(newMergedUrl);
          }
          break;
        case "merged-groups":
          // Generate merged groups PDF if not already generated
          if (!mergedGroupsUrl && packingSlipGroupsBlob) {
            await generateMergedGroupsView(packingSlipGroupsBlob);
          }
          break;
        case "labels-groups":
          // Fetch group labels if not already fetched
          if (Object.keys(groupLabels).length === 0) {
            try {
              const groupLabelsData = await fetchOrderDelyvaLabelsPerGroup(
                order?.id?.toString()
              );
              setGroupLabels(groupLabelsData.groups || {});
            } catch (error) {
              console.error("Error fetching group labels:", error);
              setGroupLabels({});
            }
          }
          break;
        default:
          break;
      }
    } catch (error) {
      console.error(`Error changing view to ${mode}:`, error);
      setError(`Failed to display ${mode} view. Please try again.`);
      // Fall back to label view
      setViewMode("label");
    } finally {
      setLoading(false);
    }
  }

  function getActiveTabClass(currentMode: ViewMode) {
    return cn(
      "px-4 py-2 text-sm font-medium rounded-md focus:outline-none",
      viewMode === currentMode
        ? "bg-skin-primary text-white"
        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
    );
  }

  function getCurrentPdfContent() {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-[500px]">
          <Loader text="Loading document..." />
        </div>
      );
    }

    switch (viewMode) {
      case "packing-slip":
        return (
          <PDFViewer
            innerRef={pdfFrame}
            className="w-full h-[1000px]"
            showToolbar={false}
          >
            <PackingSlipPdf data={order} />
          </PDFViewer>
        );
      case "label":
        return labelUrl ? (
          <iframe
            src={labelUrl}
            className="w-full h-[1000px]"
            title="Shipping Label"
            ref={pdfFrame}
          />
        ) : (
          <div className="flex items-center justify-center h-[500px]">
            <p className="text-red-500">Failed to load shipping label</p>
          </div>
        );
      case "packing-slip-groups":
        return (
          <PDFViewer
            innerRef={pdfFrame}
            className="w-full h-[1000px]"
            showToolbar={false}
          >
            <PackingSlipPerGroupDocument data={order} />
          </PDFViewer>
        );
      case "labels-groups":
        return (
          <div className="space-y-4">
            {Object.entries(groupLabels).map(([groupName, groupData]) => (
              <div key={groupName} className="border rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-2">
                  Group: {groupName === "ungrouped" ? "No Group" : groupName}
                </h3>
                <div className="text-sm text-gray-600 mb-2">
                  Shipment ID: {groupData.shipment_id}
                </div>
                <div className="text-sm text-gray-600 mb-4">
                  Products:{" "}
                  {groupData.products
                    ?.map((p: any) => `${p.name} (${p.quantity})`)
                    .join(", ")}
                </div>
                {groupData.awb_url ? (
                  <iframe
                    src={groupData.awb_url}
                    className="w-full h-[600px]"
                    title={`AWB Label - ${groupName}`}
                  />
                ) : (
                  <div className="flex items-center justify-center h-[200px] bg-gray-100">
                    <p className="text-gray-500">
                      No AWB label available for this group
                    </p>
                  </div>
                )}
              </div>
            ))}
            {Object.keys(groupLabels).length === 0 && (
              <div className="flex items-center justify-center h-[500px]">
                <p className="text-red-500">No group labels available</p>
              </div>
            )}
          </div>
        );
      case "merged":
        return mergedUrl ? (
          <iframe
            src={mergedUrl}
            className="w-full h-[1000px]"
            title="Merged Document"
            ref={pdfFrame}
          />
        ) : (
          <div className="flex items-center justify-center h-[500px]">
            <Loader text="Generating combined document..." />
          </div>
        );
      case "merged-groups":
        return mergedGroupsUrl ? (
          <iframe
            src={mergedGroupsUrl}
            className="w-full h-[1000px]"
            title="Merged Groups Document"
            ref={pdfFrame}
          />
        ) : (
          <div className="flex items-center justify-center h-[500px]">
            <Loader text="Generating combined groups document..." />
          </div>
        );
      default:
        return null;
    }
  }

  function getDownloadUrl() {
    switch (viewMode) {
      case "label":
        return labelUrl;
      case "merged":
        return mergedUrl;
      case "merged-groups":
        return mergedGroupsUrl;
      default:
        return null;
    }
  }

  function getDownloadFileName() {
    const orderId = order?.display_id || "order";
    switch (viewMode) {
      case "packing-slip":
        return `packing-slip-${orderId}.pdf`;
      case "packing-slip-groups":
        return `packing-slip-groups-${orderId}.pdf`;
      case "label":
        return `shipping-label-${orderId}.pdf`;
      case "labels-groups":
        return `shipping-labels-groups-${orderId}.pdf`;
      case "merged":
        return `complete-document-${orderId}.pdf`;
      case "merged-groups":
        return `complete-document-groups-${orderId}.pdf`;
      default:
        return `document-${orderId}.pdf`;
    }
  }

  return (
    <>
      <div className="w-full flex flex-wrap justify-between items-center border-b p-4 mb-6">
        <div className="flex items-center gap-3 mb-4 md:mb-0">
          {viewMode === "packing-slip" ? (
            <PDFDownloadLink
              document={<PackingSlipPdf data={order} />}
              fileName={getDownloadFileName()}
            >
              {({ loading: downloadLoading }: any) => (
                <Button
                  className="flex items-center gap-0.5"
                  disabled={downloadLoading}
                >
                  <Download className="w-4 h-4" />
                  {downloadLoading ? "Preparing..." : "Download"}
                </Button>
              )}
            </PDFDownloadLink>
          ) : viewMode === "packing-slip-groups" ? (
            <PDFDownloadLink
              document={<PackingSlipPerGroupDocument data={order} />}
              fileName={getDownloadFileName()}
            >
              {({ loading: downloadLoading }: any) => (
                <Button
                  className="flex items-center gap-0.5"
                  disabled={downloadLoading}
                >
                  <Download className="w-4 h-4" />
                  {downloadLoading ? "Preparing..." : "Download"}
                </Button>
              )}
            </PDFDownloadLink>
          ) : (
            <a
              href={getDownloadUrl() || "#"}
              download={getDownloadFileName()}
              className={
                !getDownloadUrl() ? "pointer-events-none opacity-50" : ""
              }
            >
              <Button
                className="flex items-center gap-0.5"
                disabled={!getDownloadUrl()}
              >
                <Download className="w-4 h-4" />
                Download
              </Button>
            </a>
          )}
          <Button
            className="flex items-center bg-gray-600 hover:bg-gray-700 gap-1"
            onClick={handlePrint}
          >
            <ImPrinter className="w-4 h-4" />
            Print
          </Button>
        </div>
        {error && (
          <div className="text-red-500 text-sm w-full mt-2">{error}</div>
        )}
      </div>

      <div className="px-4 mb-6">
        <div className="flex flex-wrap gap-2 bg-gray-100 p-1 rounded-lg">
          <button
            className={getActiveTabClass("merged-groups")}
            onClick={() => handleViewModeChange("merged-groups")}
          >
            Combined (Groups)
          </button>
          <button
            className={getActiveTabClass("merged")}
            onClick={() => handleViewModeChange("merged")}
          >
            Combined View
          </button>
          <button
            className={getActiveTabClass("labels-groups")}
            onClick={() => handleViewModeChange("labels-groups")}
          >
            AWB Labels (Groups)
          </button>
          <button
            className={getActiveTabClass("label")}
            onClick={() => handleViewModeChange("label")}
          >
            Shipping Label
          </button>
          <button
            className={getActiveTabClass("packing-slip-groups")}
            onClick={() => handleViewModeChange("packing-slip-groups")}
          >
            Packing Slip (Groups)
          </button>
          <button
            className={getActiveTabClass("packing-slip")}
            onClick={() => handleViewModeChange("packing-slip")}
          >
            Packing Slip
          </button>
        </div>
      </div>

      <div className="p-4">{getCurrentPdfContent()}</div>
    </>
  );
};

export default PackingSlipDocument;
