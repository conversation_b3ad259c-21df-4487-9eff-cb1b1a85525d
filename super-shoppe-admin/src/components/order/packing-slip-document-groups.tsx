import { Download } from "@components/icons/download-icon";
import PackingSlipPerGroupDocument, {
  PackingSlipPerGroupPdf,
} from "./packing-slip-per-group-pdf";
import Button from "@components/ui/button";
import { PDFDownloadLink, PDFViewer, pdf, Document } from "@react-pdf/renderer";
import { Order } from "@ts-types/generated";
import { RefObject, useRef, useState, useEffect } from "react";
import { ImPrinter } from "react-icons/im";
import { mergePdfs } from "@utils/pdf-merge";
import { fetchOrderDelyvaLabelsPerGroup } from "@data/order/use-order-delyva-label.query";
import {
  getGroupDisplayName,
  groupOrderByProducts,
} from "@utils/group-order-by-products";
import Loader from "@components/ui/loader/loader";
import cn from "classnames";

type ViewMode = "awb-packing-slip-groups" | "combined-groups";

const PackingSlipDocument = ({ order }: { order: Order }) => {
  const pdfFrame = useRef() as RefObject<HTMLIFrameElement>;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>("combined-groups");
  const [packingSlipGroupsBlob, setPackingSlipGroupsBlob] =
    useState<Blob | null>(null);
  const [combinedGroupsUrl, setCombinedGroupsUrl] = useState<string | null>(
    null
  );
  const [groupLabels, setGroupLabels] = useState<Record<string, any>>({});
  const [groupMergedUrls, setGroupMergedUrls] = useState<
    Record<string, string>
  >({});

  useEffect(() => {
    const generatePackingSlipBlob = async () => {
      try {
        // Generate group-based packing slip
        const groupBlob = await pdf(
          <PackingSlipPerGroupDocument data={order} />
        ).toBlob();
        setPackingSlipGroupsBlob(groupBlob);

        // Fetch group labels
        try {
          const groupLabelsData = await fetchOrderDelyvaLabelsPerGroup(
            order?.id?.toString()
          );
          const fetchedGroupLabels = groupLabelsData.groups || {};
          setGroupLabels(fetchedGroupLabels);

          // Generate individual group merged PDFs for awb-packing-slip-groups view
          if (Object.keys(fetchedGroupLabels).length > 0) {
            try {
              await generateGroupMergedPdfsWithLabels(
                fetchedGroupLabels,
                groupBlob
              );
            } catch (error) {
              console.error("Error generating group merged PDFs:", error);
            }
          }
        } catch (error) {
          console.error("Error fetching group labels:", error);
          setGroupLabels({});
        }

        // If viewMode is combined-groups, proceed to generate the combined view
        if (viewMode === "combined-groups") {
          setLoading(true);
          try {
            await generateCombinedGroupsViewWithLabels(
              fetchedGroupLabels,
              groupBlob
            );
          } catch (error) {
            console.error("Error generating combined groups PDF:", error);
            setError("Failed to generate combined groups PDF");
            setViewMode("awb-packing-slip-groups");
          } finally {
            setLoading(false);
          }
        }
      } catch (error) {
        console.error("Error generating packing slip PDF:", error);
        setError("Failed to generate packing slip PDF");
      }
    };

    generatePackingSlipBlob();
  }, [order]);

  async function handlePrint() {
    if (pdfFrame && pdfFrame.current) {
      if (pdfFrame.current.src) {
        pdfFrame.current.contentWindow?.print();
      }
    }
  }

  async function generateCombinedGroupsViewWithLabels(
    labels: Record<string, any>,
    groupBlob: Blob
  ) {
    // Get all group label URLs and merge them with individual group packing slips
    const allPdfs: string[] = [];
    const groupedOrders = groupOrderByProducts(order);

    // For each group, add AWB label followed by its specific packing slip
    for (const [groupName, groupData] of Object.entries(labels)) {
      if (groupData.awb_url) {
        // Add AWB label
        allPdfs.push(groupData.awb_url);

        // Add corresponding packing slip for this group
        const singleGroupOrder = groupedOrders.find(
          (go) => go.group_name === groupName
        );
        if (singleGroupOrder) {
          try {
            const singleGroupBlob = await pdf(
              <Document>
                <PackingSlipPerGroupPdf data={singleGroupOrder} />
              </Document>
            ).toBlob();
            allPdfs.push(URL.createObjectURL(singleGroupBlob));
          } catch (error) {
            console.error(
              `Error generating packing slip for group ${groupName}:`,
              error
            );
          }
        }
      }
    }

    // If no AWB labels, just use the complete group packing slip
    if (allPdfs.length === 0) {
      allPdfs.push(URL.createObjectURL(groupBlob));
    }

    if (allPdfs.length > 1) {
      // Merge all PDFs into one combined document
      const mergedPdfBlob = await mergePdfs(allPdfs);
      const newCombinedUrl = URL.createObjectURL(mergedPdfBlob);
      setCombinedGroupsUrl(newCombinedUrl);
    } else {
      // Only one PDF, use it directly
      setCombinedGroupsUrl(allPdfs[0]);
    }
  }

  async function generateCombinedGroupsView(groupBlob: Blob) {
    // Use the existing groupLabels state
    await generateCombinedGroupsViewWithLabels(groupLabels, groupBlob);
  }

  async function generateGroupMergedPdfsWithLabels(
    labels: Record<string, any>,
    groupBlob: Blob
  ) {
    // Generate individual merged PDFs for each group (AWB + packing slip for that group)
    const newGroupMergedUrls: Record<string, string> = {};

    for (const [groupName, groupData] of Object.entries(labels)) {
      if (groupData.awb_url) {
        try {
          // Create a single-group packing slip using the grouped order utility
          const groupedOrders = groupOrderByProducts(order);
          const singleGroupOrder = groupedOrders.find(
            (go) => go.group_name === groupName
          );

          if (!singleGroupOrder) {
            console.error(`Group ${groupName} not found in grouped orders`);
            continue;
          }
          console.log("singleGroupBlob");
          const singleGroupBlob = await pdf(
            <Document>
              <PackingSlipPerGroupPdf data={singleGroupOrder} />
            </Document>
          ).toBlob();
          console.log(singleGroupBlob);
          // Merge AWB + single group packing slip
          const mergedBlob = await mergePdfs([
            groupData.awb_url,
            URL.createObjectURL(singleGroupBlob),
          ]);

          newGroupMergedUrls[groupName] = URL.createObjectURL(mergedBlob);
        } catch (error) {
          console.error(
            `Error generating merged PDF for group ${groupName}:`,
            error
          );
        }
      }
    }

    setGroupMergedUrls(newGroupMergedUrls);
  }

  async function generateGroupMergedPdfs() {
    // Use the existing groupLabels and packingSlipGroupsBlob
    if (packingSlipGroupsBlob) {
      await generateGroupMergedPdfsWithLabels(
        groupLabels,
        packingSlipGroupsBlob
      );
    }
  }

  async function handleViewModeChange(mode: ViewMode) {
    try {
      setLoading(true);
      setError(null);
      setViewMode(mode);

      switch (mode) {
        case "combined-groups":
          if (!combinedGroupsUrl && packingSlipGroupsBlob) {
            await generateCombinedGroupsView(packingSlipGroupsBlob);
          }
          break;
        case "awb-packing-slip-groups":
          // Generate individual merged PDFs for each group if not already done
          if (
            Object.keys(groupMergedUrls).length === 0 &&
            Object.keys(groupLabels).length > 0
          ) {
            await generateGroupMergedPdfs();
          }
          break;
        default:
          break;
      }
    } catch (error) {
      console.error(`Error changing view to ${mode}:`, error);
      setError(`Failed to display ${mode} view. Please try again.`);
      setViewMode("awb-packing-slip-groups");
    } finally {
      setLoading(false);
    }
  }

  function getActiveTabClass(currentMode: ViewMode) {
    return cn(
      "px-4 py-2 text-sm font-medium rounded-md focus:outline-none",
      viewMode === currentMode
        ? "bg-skin-primary text-white"
        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
    );
  }

  function getCurrentPdfContent() {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-[500px]">
          <Loader text="Loading document..." />
        </div>
      );
    }

    switch (viewMode) {
      case "awb-packing-slip-groups":
        return (
          <div className="space-y-6">
            {Object.entries(groupLabels).map(([groupName, groupData]) => (
              <div
                key={groupName}
                className="border rounded-lg p-6 bg-white shadow-sm"
              >
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-xl font-semibold">
                    Group: {getGroupDisplayName(groupName)}
                  </h3>
                  <div className="flex gap-2">
                    {groupMergedUrls[groupName] && (
                      <a
                        href={groupMergedUrls[groupName]}
                        download={`awb-packing-slip-${groupName}-${
                          order?.display_id || "order"
                        }.pdf`}
                        className="inline-flex items-center gap-1 px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                      >
                        <Download className="w-4 h-4" />
                        Download
                      </a>
                    )}
                    {groupMergedUrls[groupName] && (
                      <button
                        onClick={() => {
                          const iframe = document.createElement("iframe");
                          iframe.src = groupMergedUrls[groupName];
                          iframe.style.display = "none";
                          document.body.appendChild(iframe);
                          iframe.onload = () => {
                            iframe.contentWindow?.print();
                            document.body.removeChild(iframe);
                          };
                        }}
                        className="inline-flex items-center gap-1 px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
                      >
                        <ImPrinter className="w-4 h-4" />
                        Print
                      </button>
                    )}
                  </div>
                </div>
                <div className="text-sm text-gray-600 mb-2">
                  Shipment ID: {groupData.shipment_id}
                </div>
                <div className="text-sm text-gray-600 mb-4">
                  Products:{" "}
                  {groupData.products
                    ?.map((p: any) => `${p.name} (${p.quantity})`)
                    .join(", ")}
                </div>
                {groupMergedUrls[groupName] ? (
                  <iframe
                    src={groupMergedUrls[groupName]}
                    className="w-full h-[800px] border rounded"
                    title={`AWB + Packing Slip - ${groupName}`}
                  />
                ) : (
                  <div className="flex items-center justify-center h-[200px] bg-gray-100 rounded">
                    <p className="text-gray-500">
                      {groupData.awb_url
                        ? "Generating merged document..."
                        : "No AWB label available for this group"}
                    </p>
                  </div>
                )}
              </div>
            ))}
            {Object.keys(groupLabels).length === 0 && (
              <div className="flex items-center justify-center h-[500px]">
                <p className="text-red-500">No group labels available</p>
              </div>
            )}
          </div>
        );
      case "combined-groups":
        return combinedGroupsUrl ? (
          <iframe
            src={combinedGroupsUrl}
            className="w-full h-[1000px]"
            title="Combined Groups Document"
            ref={pdfFrame}
          />
        ) : (
          <div className="flex items-center justify-center h-[500px]">
            <Loader text="Generating combined groups document..." />
          </div>
        );
      default:
        return null;
    }
  }

  function getDownloadUrl() {
    switch (viewMode) {
      case "combined-groups":
        return combinedGroupsUrl;
      default:
        return null;
    }
  }

  function getDownloadFileName() {
    const orderId = order?.display_id || "order";
    switch (viewMode) {
      case "awb-packing-slip-groups":
        return `awb-packing-slip-groups-${orderId}.pdf`;
      case "combined-groups":
        return `combined-groups-${orderId}.pdf`;
      default:
        return `document-${orderId}.pdf`;
    }
  }

  return (
    <>
      <div className="w-full flex flex-wrap justify-between items-center border-b p-4 mb-6">
        <div className="flex items-center gap-3 mb-4 md:mb-0">
          {viewMode === "combined-groups" && (
            <a
              href={getDownloadUrl() || "#"}
              download={getDownloadFileName()}
              className={
                !getDownloadUrl() ? "pointer-events-none opacity-50" : ""
              }
            >
              <Button
                className="flex items-center gap-0.5"
                disabled={!getDownloadUrl()}
              >
                <Download className="w-4 h-4" />
                Download All
              </Button>
            </a>
          )}
          <Button
            className="flex items-center bg-gray-600 hover:bg-gray-700 gap-1"
            onClick={handlePrint}
          >
            <ImPrinter className="w-4 h-4" />
            Print
          </Button>
        </div>
        {error && (
          <div className="text-red-500 text-sm w-full mt-2">{error}</div>
        )}
      </div>

      <div className="px-4 mb-6">
        <div className="flex flex-wrap gap-2 bg-gray-100 p-1 rounded-lg">
          <button
            className={getActiveTabClass("combined-groups")}
            onClick={() => handleViewModeChange("combined-groups")}
          >
            Combined (Groups)
          </button>
          <button
            className={getActiveTabClass("awb-packing-slip-groups")}
            onClick={() => handleViewModeChange("awb-packing-slip-groups")}
          >
            AWB Labels & Packing slip (Groups)
          </button>
        </div>
      </div>

      <div className="p-4">{getCurrentPdfContent()}</div>
    </>
  );
};

export default PackingSlipDocument;
