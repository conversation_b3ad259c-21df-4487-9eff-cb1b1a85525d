import Order from "@repositories/order";
import { useQuery } from "react-query";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import http from "@utils/api/http";

export const fetchOrderDelyvaLabel = async (id: string) => {
  const { data } = await Order.find(
    `${API_ENDPOINTS.ORDERS}/${id}/delyva/label`
  );
  return data;
};

export const fetchOrderDelyvaLabelsPerGroup = async (id: string) => {
  const { data } = await Order.find(
    `${API_ENDPOINTS.ORDERS}/${id}/delyva/labels/groups`
  );
  return data;
};

export const fetchOrderDelyvaLabelBlob = async (id: string) => {
  const { data } = await http.get(
    `${API_ENDPOINTS.ORDERS}/${id}/delyva/label/blob`,
    { responseType: "arraybuffer" }
  );
  return data;
};

type OrderDelyvaLabelResponse = {
  url: string;
};

type OrderDelyvaGroupLabel = {
  shipment_id: string;
  awb_url: string;
  products: Array<{
    id: number;
    name: string;
    quantity: number;
  }>;
};

type OrderDelyvaLabelsPerGroupResponse = {
  groups: Record<string, OrderDelyvaGroupLabel>;
};

export const useOrderDelyvaLabelQuery = (id: string, options: any = {}) => {
  return useQuery<OrderDelyvaLabelResponse, Error>(
    [`${API_ENDPOINTS.ORDERS}/${id}/delyva/label`],
    () => fetchOrderDelyvaLabel(id),
    {
      ...options,
      keepPreviousData: true,
      staleTime: 60 * 1000,
    }
  );
};

export const useOrderDelyvaLabelsPerGroupQuery = (
  id: string,
  options: any = {}
) => {
  return useQuery<OrderDelyvaLabelsPerGroupResponse, Error>(
    [`${API_ENDPOINTS.ORDERS}/${id}/delyva/labels/groups`],
    () => fetchOrderDelyvaLabelsPerGroup(id),
    {
      ...options,
      keepPreviousData: true,
      staleTime: 60 * 1000,
    }
  );
};
