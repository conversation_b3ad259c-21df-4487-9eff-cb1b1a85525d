<?php
/*
Revision History
RH#     Ref.No.        Date            Who         What
------------------------------------------------------------------------------------
1.      KKM0001        05-Feb-2022     Jake        Added routes for new HomePage api
2.      KKM0001        13-Feb-2022     Jake        Added routes for BannerController
*/

use App\Enums\RoleEnum;
use App\Http\Controllers\MembershipTierController;
use App\Http\Controllers\PushNotificationController;
use App\Http\Controllers\ScheduleNotificationController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\TypeController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\BannerController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\AddressController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\VoucherController;
use App\Http\Controllers\WebhookController;
use App\Http\Controllers\WishlistController;
use App\Http\Controllers\PromotionController;
use App\Http\Controllers\CouponController;
use App\Http\Controllers\CustomerGroupController;
use App\Http\Controllers\PaymentGatewayController;
use App\Http\Controllers\PaymentMethodController;
use App\Http\Controllers\OrderAttributeController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\ShipmentController;
use App\Http\Controllers\CreditMemoController;
use App\Http\Controllers\RefundController;
use App\Http\Controllers\ShippingController;
use App\Http\Controllers\AnalyticsController;
use App\Http\Controllers\Api\GoRewardsController;
use App\Http\Controllers\AttachmentController;
use App\Http\Controllers\AttributeController;
use App\Http\Controllers\AttributeValueController;
use App\Http\Controllers\BlockedDomainController;
use App\Http\Controllers\ExportHistoryController;
use App\Http\Controllers\OrderStatusController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\ReferralController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\StaticBlockController;
use App\Http\Controllers\WalletGroupController;
use App\Http\Controllers\WalletRuleController;
use App\Http\Controllers\DailyCheckinController;
use App\Http\Controllers\DiscountController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\MediaController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\WalletTransactionController;
use App\Http\Controllers\NotificationTemplateController;
use App\Http\Controllers\SearchTermController;
use App\Http\Controllers\SessionController;
use App\Http\Controllers\ShippingZoneController;
use App\Http\Controllers\WalletController;
use Illuminate\Support\Facades\Route;
use Marvel\Http\Controllers\TagController;
use Marvel\Http\Controllers\TaxController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::apiResource('attributes', AttributeController::class, [
    'only' => ['index', 'show']
]);

//KKM0001 - added routes for banner controller
Route::apiResource('banners', BannerController::class, [
    'only' => ['index', 'show']
]);
//Get banner by banner type
Route::get('banners-by-type', [BannerController::class, 'findBannerByBannerType']);
//end KKM0001

Route::prefix('cart')->group(function () {
    Route::get('/', [CartController::class, 'show']);
    Route::put('/', [CartController::class, 'update']);
});

Route::put('checkout', [CheckoutController::class, 'update']);

Route::get('checkin', [DailyCheckinController::class, 'index']);

Route::apiResource('categories', CategoryController::class, [
    'only' => ['index', 'show']
]);
Route::get('categories-nav', [CategoryController::class, 'fetchCategories']); // KKM0001

Route::apiResource('contacts', ContactController::class, [
    'only' => ['store']
]);

Route::get('fetch-nav-category', [CategoryController::class, 'fetchOnlyNavigation']);
Route::get('fetch-brand-category', [CategoryController::class, 'fetchOnlyBrand']);
Route::get('sort-brand-category', [CategoryController::class, 'fetchBrandSort']);
Route::get('fetch-search-filter', [CategoryController::class, 'fetchFilter']);

Route::post('orders/checkout', [OrderController::class, 'save']);
Route::get('frontend/orders/payment/callback/{gateway_code}', [OrderController::class, 'frontendPaymentCallback']);
Route::post('frontend/orders/payment/callback/{gateway_code}', [OrderController::class, 'frontendPaymentCallback']);
Route::post('orders/payment/callback/{gateway_code}/{action?}', [OrderController::class, 'paymentCallback']);
Route::post('orders/refund/notify/{gateway_code}', [CreditMemoController::class, 'refundNotify']);
Route::get('orders/payment/{id}/{action?}', [OrderController::class, 'payment']);
Route::get('track-order/{order_number}', [OrderController::class, 'track']);
Route::apiResource('orders', OrderController::class, [
    'only' => ['index', 'show']
]);

Route::apiResource('payment_gateways', PaymentGatewayController::class, [
    'only' => ['index']
]);
Route::get('payment_gateways/{gateway_id}/methods', [PaymentGatewayController::class, 'getPaymentMethods']);
Route::get('states', [CheckoutController::class, 'getCountryStates']);

Route::apiResource('products', ProductController::class, [
    'only' => ['index', 'show']
]);
Route::get('products-preview/{key}', [ProductController::class, 'getByPreviewKey']);
Route::get('products-search', [ProductController::class, 'search']);
Route::get('products-by-category', [ProductController::class, 'fetchProductByCategory']); //KKM0001
Route::get('popular-products', [ProductController::class, 'popularProducts']);

Route::get('/refer-code/check/{code}', [UserController::class, 'checkReferralCode']);

Route::apiResource('settings', SettingsController::class, [
    'only' => ['index']
]);
Route::get('settings/{key}', [SettingsController::class, 'getAdditionalSettingsByKey']);

Route::apiResource('static_blocks', StaticBlockController::class, [
    'only' => ['index', 'show']
]);

Route::apiResource('shippings', ShippingController::class, [
    'only' => ['index', 'show']
]);

Route::apiResource(
    'refunds',
    RefundController::class,
    [
        'only' => ['store', 'update']
    ]
);

Route::apiResource('tags', TagController::class, [
    'only' => ['index', 'show']
]);

//KKM0001 Get types with banner
Route::get('types-banner', [TypeController::class, 'fetchTypeWithBanner']);

Route::get('session/id', [SessionController::class, 'getSessionId']);

Route::middleware('throttle:register')->post('register', [UserController::class, 'register']);
Route::middleware('throttle:login')->post('token', [UserController::class, 'token']);
Route::middleware('throttle:login')->post('admin-token', [UserController::class, 'adminToken']);
Route::post('auth/pin-check', [UserController::class, 'authPinCheck']);
Route::middleware('throttle:login')->post('/social-login-token', [UserController::class, 'socialLogin']);
Route::post('auth/tng', [UserController::class, 'tngAuth']);
Route::post('/logout', [UserController::class, 'logout']);

Route::middleware('throttle:forget-password')->post('/forget-password', [UserController::class, 'forgetPassword']);
Route::middleware('throttle:send-email-verify')->post('/send-email-verify', [UserController::class, 'sendEmailVerification']);
Route::middleware('throttle:send-phone-verify')->post('/request/otp', [UserController::class, 'sendPhoneVerification']);
Route::middleware('throttle:send-account-verify')->post('/send-account-verify', [UserController::class, 'sendAccountEmailVerification']);

Route::middleware('throttle:verify-password-token')->post('/verify-forget-password-token', [UserController::class, 'verifyForgetPasswordToken']);
Route::middleware('throttle:verify-email-token')->post('/verify-email', [UserController::class, 'verifyEmail']);
Route::middleware('throttle:verify-phone-token')->post('/verify-otp', [UserController::class, 'verifyPhone']);
Route::middleware('throttle:verify-account-token')->post('/verify-account-merge', [UserController::class, 'verifyAndMergeAccount']);

Route::middleware('throttle:reset-password')->post('/reset-password', [UserController::class, 'resetPassword']);

Route::apiResource('promotions', PromotionController::class, [
    'only' => ['index', 'show']
]);

Route::apiResource('coupons', CouponController::class, [
    'only' => ['index', 'show']
]);

Route::apiResource('customer_groups', CustomerGroupController::class, [
    'only' => ['index', 'show']
]);

Route::prefix('webhook')->group(function () {
    Route::post('order/shipment/update', [WebhookController::class, 'shipmentUpdate']);
});
Route::prefix('gorewards')->middleware('auth.gorewards')->group(function () {
    Route::post('enquiry', [GoRewardsController::class, 'enquiryPoints']);
    Route::post('redeem', [GoRewardsController::class, 'redeemPoints']);
    Route::post('refund', [GoRewardsController::class, 'refundPoints']);
});

Route::get('store-list', [StoreController::class, 'index']);
Route::get('membership-tiers-list', [MembershipTierController::class, 'indexList']);
Route::group(['middleware' => ['auth:sanctum', 'check.blocked']], function () {
    Route::get('auth/setup', [UserController::class, 'authSetup']);
    Route::get('me', [UserController::class, 'me']);
    Route::put('me', [UserController::class, 'updateProfile']);
    Route::post('/save-fcm-token', [PushNotificationController::class, 'saveFcmToken']);
    Route::put('/notification-settings', [PushNotificationController::class, 'updateUserNotificationSettings']);
    Route::post('me/delete', [UserController::class, 'deleteMyAccount']);
    Route::get('qrcode', [UserController::class, 'getCode']);
    Route::get('refer-code', [UserController::class, 'getReferralCode']);
    Route::post('checkin', [DailyCheckinController::class, 'checkin']);
    Route::post('cart/buy-again', [CartController::class, 'buyAgain']);
    Route::put('address/{id?}', [AddressController::class, 'storeOrUpdate']);
    Route::get('wishlist', [UserController::class, 'getWishlistProducts']);
    Route::put('wishlist/{id?}', [WishlistController::class, 'update']);
    Route::apiResource('address', AddressController::class, [
        'only' => ['destroy']
    ]);
    Route::apiResource('referrals', ReferralController::class, [
        'only' => ['index']
    ]);
    Route::put('users/{id}', [UserController::class, 'update']);
    Route::post('change-password', [UserController::class, 'changePassword']);
    Route::get('recent-products', [UserController::class, 'getRecentlyViewedProducts']);
    Route::get('orders-overview', [OrderController::class, 'overview']);
    Route::get('orders-products', [OrderController::class, 'products']);
    Route::apiResource('wallet_transactions', WalletTransactionController::class, [
        'only' => ['index', 'show']
    ]);
    Route::apiResource('attachments', AttachmentController::class, [
        'only' => ['store']
    ]);
    Route::post('attachments/url', [AttachmentController::class, 'storeFromS3Url']);
    Route::prefix('media')->group(function () {
        Route::post('signed/url', [MediaController::class, 'getS3SignedUrl']);
    });

    Route::apiResource('branch', BranchController::class, ['only' => ['index', 'show']])->middleware(['can:view-branch']);
    Route::apiResource('branch', BranchController::class, ['only' => ['store', 'update']])->middleware(['can:edit-branch']);
    Route::apiResource('branch', BranchController::class, ['only' => ['destroy']])->middleware(['can:delete-branch']);
    Route::put('branch/{id}/products', [BranchController::class, 'updateProducts'])->middleware(['can:edit-branch|edit-product']);

    Route::apiResource('wallet/groups', WalletGroupController::class, ['only' => ['index', 'show']])->middleware(['can:view-wallet_group']);
    Route::apiResource('wallet/groups', WalletGroupController::class, ['only' => ['store', 'update']])->middleware(['can:edit-wallet_group']);

    Route::apiResource('wallet/groups/{wallet_group_id}/rules', WalletRuleController::class, ['only' => ['index', 'show']])->middleware(['can:view-wallet_rule']);
    Route::apiResource('wallet/groups/{wallet_group_id}/rules', WalletRuleController::class, ['only' => ['store', 'update']])->middleware(['can:edit-wallet_rule']);
    Route::apiResource('wallet/groups/{wallet_group_id}/rules', WalletRuleController::class, ['only' => ['destroy']])->middleware(['can:delete-wallet_rule']);

    Route::apiResource('wallets', WalletController::class, ['only' => ['show']])->middleware(['can:view-wallet']);

    Route::apiResource('address', AddressController::class, ['only' => ['index']])->middleware('can:view-address');

    Route::get('analytics', [AnalyticsController::class, 'analytics'])->middleware(['can:view-dashboard']);
    Route::get('analytics/byMonth', [AnalyticsController::class, 'byMonthAnalytic'])->middleware(['can:view-dashboard']);
    Route::post('order-analytics', [AnalyticsController::class, 'orderAnalytics'])->middleware(['can:view-order']);
    Route::post('order-analytics/export', [AnalyticsController::class, 'exportOrderAnalytics'])->middleware(['can:view-order']);

    //KKM0001 - added routes for banner controller
    Route::apiResource('banners', BannerController::class, ['only' => ['store', 'update']])->middleware(['can:edit-banner']);
    Route::apiResource('banners', BannerController::class, ['only' => ['destroy']])->middleware(['can:delete-banner']);
    Route::post('banners/search', [BannerController::class, 'search'])->middleware(['can:search-banner']);

    Route::get('discounts/{id}/usages', [DiscountController::class, 'getUsage'])->middleware(['can:view-discount']);

    //KKM0002 - added routes for promotion controller
    Route::group(['middleware' => ['can:edit-discount']], function () {
        Route::apiResource('promotions', PromotionController::class, ['only' => ['store', 'update']]);
        Route::apiResource('coupons', CouponController::class, ['only' => ['store', 'update']]);
    });
    Route::group(['middleware' => ['can:delete-discount']], function () {
        Route::apiResource('promotions', PromotionController::class, ['only' => ['destroy']]);
        Route::apiResource('coupons', CouponController::class, ['only' => ['destroy']]);
    });

    Route::apiResource('customer_groups', CustomerGroupController::class, ['only' => ['store', 'update']])->middleware(['can:edit-customer_group']);

    Route::group(['middleware' => ['can:edit-category']], function () {
        Route::put('batch-categories', [CategoryController::class, 'batchUpdate']);
        Route::apiResource('categories', CategoryController::class, ['only' => ['store', 'update']]);
        Route::put('update-nav-category', [CategoryController::class, 'updateNavigation']);
        Route::put('update-brand-category', [CategoryController::class, 'updateBrand']);
    });
    Route::apiResource('categories', CategoryController::class, ['only' => ['destroy']])->middleware(['can:delete-category']);

    Route::group(['middleware' => ['can:edit-category|edit-product']], function () {
        Route::put('categories/{id}/products', [CategoryController::class, 'updateProducts']);
        Route::put('categories/{id}/sorting', [CategoryController::class, 'updateProductSorting']);
    });

    Route::apiResource('export-history', ExportHistoryController::class, ['only' => ['index']])
        ->middleware(['permission:view-product|view-order|view-search_term|view-user|view-wallet_transaction']);

    Route::apiResource('locations', LocationController::class, ['only' => ['index', 'show']])->middleware(['can:view-location']);
    Route::apiResource('locations', LocationController::class, ['only' => ['store', 'update']])->middleware(['can:edit-location']);
    Route::apiResource('locations', LocationController::class, ['only' => ['destroy']])->middleware(['can:delete-location']);

    Route::group(['middleware' => ['can:view-order']], function () {
        Route::post('orders/export', [OrderController::class, 'export']);
        Route::post('orders/selected', [OrderController::class, 'getSelectedOrders']);
        Route::apiResource('invoices', InvoiceController::class, ['only' => ['show']]);
        Route::apiResource('shipments', ShipmentController::class, ['only' => ['show']]);
        Route::apiResource('credit-memo', CreditMemoController::class, ['only' => ['show']]);
        Route::apiResource('payment_methods', PaymentMethodController::class, ['only' => ['index', 'show']]);
        Route::apiResource('order-attributes', OrderAttributeController::class);
    });
    Route::group(['middleware' => ['can:edit-order']], function () {
        Route::post('orders/checkout/verify', [CheckoutController::class, 'verify']);
        Route::apiResource('orders', OrderController::class, ['only' => ['store', 'update']]);
        Route::post('orders/{id}/invoice', [OrderController::class, 'invoice']);
        Route::post('orders/{id}/ship', [OrderController::class, 'ship']);
        Route::post('orders/{id}/refund', [OrderController::class, 'refund']);
        Route::get('orders/{id}/delyva/label', [OrderController::class, 'delyvaPrintLabel']);
        Route::get('orders/{id}/delyva/labels/groups', [OrderController::class, 'delyvaPrintLabelsPerGroup']);
        Route::get('orders/{id}/delyva/label/blob', [OrderController::class, 'delyvaPrintLabelBlob']);
        Route::put('orders/{id}/attributes', [OrderController::class, 'updateOrderAttributes']);
        Route::post('credit-memo/{id}/refund', [CreditMemoController::class, 'refund']);
        Route::post('orders/bulk-update-status', [OrderController::class, 'bulkUpdateStatus']);
    });
    Route::apiResource('orders', OrderController::class, ['only' => ['destroy']])->middleware(['can:delete-order']);

    Route::apiResource('order-status', OrderStatusController::class, ['only' => ['store', 'update', 'destroy']])->middleware(['permission:view-order|edit-order_status']);

    Route::apiResource('notification_templates', NotificationTemplateController::class, ['only' => ['index', 'store', 'show', 'update', 'destroy']])->middleware(['can:edit-notification_template']);

    Route::apiResource('payment_gateways', PaymentGatewayController::class, ['only' => ['show', 'store', 'update', 'destroy']])->middleware(['can:edit-payment_gateway']);

    Route::apiResource('payment_methods', PaymentMethodController::class)->middleware(['permission:edit-payment_method|view-order']);

    Route::group(['middleware' => ['can:edit-product']], function () {
        Route::apiResource('products', ProductController::class, ['only' => ['store', 'update']]);
        Route::post('products/import', [ProductController::class, 'import']);
        Route::post('products/preview', [ProductController::class, 'getPreviewKey']);
        Route::put('products/quick-update/{id}', [ProductController::class, 'quickUpdate']);
        Route::apiResource('attributes', AttributeController::class, [
            'only' => ['store', 'update', 'destroy']
        ]);
        Route::apiResource('attribute-values', AttributeValueController::class, [
            'only' => ['store', 'update', 'destroy']
        ]);
    });
    Route::apiResource('products', ProductController::class, ['only' => ['destroy']])->middleware(['can:delete-product']);
    Route::post('products/export', [ProductController::class, 'export'])->middleware(['can:view-product']);

    Route::apiResource('settings', SettingsController::class, ['only' => ['store']])->middleware(['can:edit-setting']);
    Route::post('settings/{key}', [SettingsController::class, 'storeAdditionalSettingsByKey'])->middleware(['can:edit-setting']);

    Route::apiResource('shippings', ShippingController::class, ['only' => ['store', 'update', 'destroy']])->middleware(['can:edit-shipping']);
    Route::apiResource('shipping_zones', ShippingZoneController::class)->middleware(['can:edit-shipping']);

    Route::group(['middleware' => ['can:edit-static_block']], function () {
        Route::apiResource('static_blocks', StaticBlockController::class, ['only' => ['store', 'update', 'destroy']]);
        Route::put('sort-static-block', [StaticBlockController::class, 'updateStaticBlockSorting']);
    });

    Route::group(['middleware' => ['can:view-search_term']], function () {
        Route::apiResource('search_terms', SearchTermController::class, ['only' => ['index']]);
        Route::post('search_terms/export', [SearchTermController::class, 'export']);
    });

    Route::apiResource('search_terms', SearchTermController::class, ['only' => ['update']])->middleware(['can:edit-search_term']);

    Route::apiResource('tags', TagController::class, ['only' => ['store', 'update', 'destroy']])->middleware(['can:edit-tag']);

    Route::apiResource('taxes', TaxController::class)->middleware(['can:edit-tax']);

    Route::group(['middleware' => ['can:view-user']], function () {
        Route::apiResource('users', UserController::class, ['only' => ['index', 'show']]);
        Route::post('users/export', [UserController::class, 'export']);
    });
    Route::group(['middleware' => ['can:edit-user']], function () {
        Route::apiResource('users', UserController::class, ['only' => ['store', 'update']]);
        Route::post('users/import', [UserController::class, 'import']);
        Route::post('users/block-user', [UserController::class, 'banUser']);
        Route::post('users/unblock-user', [UserController::class, 'activeUser']);
        Route::post('users/merge-account', [UserController::class, 'mergeAccounts']);
    });
    Route::post('users/delete/{id}', [UserController::class, 'destroy'])->middleware(['can:delete-user']);
    Route::post('users/restore/{id}', [UserController::class, 'restore'])->middleware(['can:restore-user']);

    Route::post('wallet_transactions', [WalletTransactionController::class, 'store'])->middleware(['permission:edit-user|view-wallet_transaction']);

    Route::post('wallet_transactions/export', [WalletTransactionController::class, 'export'])->middleware(['can:view-wallet_transaction']);

    Route::apiResource('roles', RoleController::class)->middleware(['can:edit-role_permission']);

    Route::apiResource('blocked_domains', BlockedDomainController::class)->middleware(['can:edit-blocked_domain']);

    // only admin
    Route::group(['middleware' => 'role:' . RoleEnum::SUPER_ADMIN . '|' . RoleEnum::POS], function () {
        Route::get('users/qrcode/{token}', [UserController::class, 'getByCode']);

        Route::prefix('offline')->group(function () {
            Route::post('transaction', [WalletTransactionController::class, 'offlineStore']);
            Route::get('transaction/{sale_id}', [WalletTransactionController::class, 'offlineShow']);
        });
    });

    Route::prefix('voucher')->group(function () {
        Route::post('activated/{voucher}', [VoucherController::class, 'activated']);
    });
    Route::group(['middleware' => ['can:edit-stores']], function () {
        Route::apiResource('stores', StoreController::class);
        Route::put('stores/{id}/status', [StoreController::class, 'changeStatus']);
        Route::post('stores/import', [StoreController::class, 'import']);
    });

    Route::group(['middleware' => ['can:edit-notification_template']], function () {
        Route::apiResource('schedule-notifications', ScheduleNotificationController::class)->middleware(['can:edit-notification_template']);
    });

    Route::apiResource('notifications', NotificationController::class, ['only' => ['index', 'show']]);
    Route::put('notifications/read/{id}', [NotificationController::class, 'markAsRead']);
    Route::apiResource('membership-tiers', MembershipTierController::class)->middleware(['can:edit-membership']);
    Route::post('membership-tiers/wallet-rule-action', [MembershipTierController::class, 'triggerAction']);

    Route::post('proxy/pdf', [OrderController::class, 'proxyPdf']);
});
