<?php

namespace App\Database\Models;

use App\Scopes\BranchScope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Arr;

class OrderProduct extends Model
{
    use \Awobaz\Compoships\Compoships;

    protected $table = 'order_product';

    public $guarded = [];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class)
            ->withoutGlobalScopes([BranchScope::class, 'status_permission']);
    }

    public function inventory()
    {
        return $this->hasOne(Inventory::class, ['product_id', 'variation_option_id'], ['product_id', 'variation_option_id']);
    }

    public function coupons()
    {
        return $this->hasMany(Coupon::class);
    }

    public function getProductGroupNameAttribute()
    {
        return Arr::get($this->product->meta ?: [], 'product_group_name');
    }
}
