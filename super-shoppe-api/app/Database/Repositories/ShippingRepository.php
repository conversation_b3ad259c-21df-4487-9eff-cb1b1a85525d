<?php

namespace App\Database\Repositories;

use App\Database\Models\Cart;
use App\Services\MembershipService;
use App\Services\ShippingService;
use App\Traits\Carts;
use App\Enums\ShippingType;
use App\Database\Models\Shipping;

use Illuminate\Http\Request;
use Prettus\Validator\Exceptions\ValidatorException;

use Marvel\Database\Repositories\ShippingRepository as MarvelShippingRepository;

class ShippingRepository extends MarvelShippingRepository
{
    use Carts;


    protected $membershipService;
    protected $shippingService;
    public function __construct(MembershipService $membershipService, ShippingService $shippingService)
    {
        parent::__construct(app());
        $this->membershipService = $membershipService;
        $this->shippingService = $shippingService;
    }


    protected $dataArray = [
        'code',
        'name',
        'description',
        'amount',
        'is_global',
        'type',
        'is_active',
        'pickup_details', // DDB field
    ];

    public function getByCart(Cart $cart)
    {
        // check cart with shipping rule
        if ($this->withCartRule($cart)->active()->exists()) {
            $shippings = $this->withCartRule($cart)->orWhere(fn($query) => $query->where('type', '!=', 'dynamic')->withoutRule())->active()->get();
        } else {
            // no matching rule, therefore get by type
            if ($cart->state_id) {
                $shippings = $this->where(
                    fn($query) => $query->where('type', 'dynamic')
                        ->whereHas('states', fn($q) => $q->where('states.id', $cart->state_id)) // dynamic shipping by state
                        ->orWhere(fn($query) => $query->where('type', '!=', 'dynamic')->withoutRule())
                )
                    ->active()
                    ->get();
            }
            if (!$shippings || $shippings->count() <= 0) {
                $shippings = $this->where(fn($query) => $query->where('type', '!=', 'dynamic')->withoutRule())->active()->get(); // not dynamic shipping class and does not have any allocated shipping rule
            }
        }

        return $this->populateShippingFee($cart, $shippings);
    }

    public function getWithoutDynamicType($cart)
    {
        // check cart with shipping rule
        if ($this->withCartRule($cart)->exists()) {
            $shippings = $this->withCartRule($cart)->get();
        } else {
            $shippings = $this->where(fn($query) => $query->where('type', '!=', 'dynamic')->whereDoesntHave('shipping_rule'))->get();
        }
        return $this->populateShippingFee($cart, $shippings);
    }

    protected function populateShippingFee(Cart $cart, $shippings)
    {
        return $shippings->map(function ($shipping) use ($cart) {
            $shippingValue = $this->shippingService->getShippingValue($shipping, $cart);
            $shipping->amount = is_array($shippingValue) ? $shippingValue['shipping_fee'] : $shippingValue;
            return $shipping;
        })->reject(function ($shipping) {
            return is_null($shipping->amount);
        })->values();
    }

    public function storeShipping(Request $request)
    {
        try {
            $data = $request->only($this->dataArray);
            $shipping = $this->create($data);
            if (isset($request['shipping_rule'])) {
                $shipping_rule_data = array_diff_key($request['shipping_rule'], ['membership_tiers' => '']);

                $shipping_rule = $shipping->shipping_rule()->create($shipping_rule_data);

                $this->membershipService->bindShippingRuleWithMembership(
                    $shipping->shipping_rule->id,
                    $request['shipping_rule']['membership_tiers'],
                );
            } else {
                $shipping_rule = $shipping->shipping_rule()->create([]);
            }
            if ($shipping->type == ShippingType::DYNAMIC) {
                if (isset($request['states'])) {
                    $shipping->states()->attach($request['states']);
                }
                if (isset($request['shipping_rates'])) {
                    if (isset($request['shipping_rates']['upsert'])) {
                        foreach ($request['shipping_rates']['upsert'] as $rate) {
                            $shipping->shipping_rates()->create($rate);
                        }
                    }
                }
            }
            if ((isset($request['applicable_products']) || isset($request['non_applicable_products'])) && $shipping_rule) {
                $eligibleProducts = array_fill_keys($request['applicable_products'] ?: [], ['is_applicable' => true]);
                $nonEligibleProducts = array_fill_keys($request['non_applicable_products'] ?: [], ['is_applicable' => false]);
                $products = $nonEligibleProducts + $eligibleProducts;
                $shipping_rule->products()->sync($products);
            }
            if (isset($request['customer_groups'])) {
                $shipping->customer_groups()->attach($request['customer_groups']);
            }
            $shipping->shipping_rule = $shipping->shipping_rule;
            $shipping->states = $shipping->states;
            $shipping->shipping_rates = $shipping->shipping_rates;
            $shipping->customer_groups = $shipping->customer_groups;
            return $shipping;
        } catch (ValidatorException $e) {
            throw new \Exception('ERROR.SOMETHING_WENT_WRONG');
        }
    }

    public function updateShipping(Request $request, $id)
    {
        try {
            $shipping = $this->findOrFail($id);
            $shipping_rule = $shipping->shipping_rule;
            if (isset($request['shipping_rule'])) {

                $shipping_rule_data = array_diff_key($request['shipping_rule'], ['membership_tiers' => '']);

                if ($shipping->shipping_rule)
                    $shipping->shipping_rule()->update($shipping_rule_data);
                else
                    $shipping_rule = $shipping->shipping_rule()->create($shipping_rule_data);

                if (isset($request['shipping_rule']['membership_tiers'])) {
                    $this->membershipService->bindShippingRuleWithMembership(
                        $shipping_rule->id,
                        $request['shipping_rule']['membership_tiers'],
                    );
                }
            } else if (!$shipping_rule) {
                $shipping_rule = $shipping->shipping_rule()->create([]);
            }
            if (isset($request['states'])) {
                $shipping->states()->sync($request['states']);
            }
            if (isset($request['shipping_rates'])) {
                if (isset($request['shipping_rates']['upsert'])) {
                    foreach ($request['shipping_rates']['upsert'] as $rate) {
                        if (isset($rate['id'])) {
                            $shipping->shipping_rates()->where('id', $rate['id'])->update($rate);
                        } else {
                            $shipping->shipping_rates()->create($rate);
                        }
                    }
                }
                if (isset($request['shipping_rates']['delete'])) {
                    foreach ($request['shipping_rates']['delete'] as $id) {
                        try {
                            $shipping->shipping_rates()->where('id', $id)->delete();
                        } catch (\Exception $e) {
                        }
                    }
                }
            }
            if ((isset($request['applicable_products']) || isset($request['non_applicable_products'])) && $shipping_rule) {
                $eligibleProducts = array_fill_keys($request['applicable_products'] ?: [], ['is_applicable' => true]);
                $nonEligibleProducts = array_fill_keys($request['non_applicable_products'] ?: [], ['is_applicable' => false]);
                $products = $nonEligibleProducts + $eligibleProducts;
                $shipping_rule->products()->sync($products);
            }
            if (isset($request['customer_groups'])) {
                $shipping->customer_groups()->sync($request['customer_groups']);
            }
            $shipping->update($request->only($this->dataArray));
            if (!in_array($shipping->type, [ShippingType::DYNAMIC, ShippingType::DELYVA])) {
                $shipping->states()->detach();
                $shipping->shipping_rates()->delete();
            }
            $shipping->shipping_rule = $shipping->shipping_rule;
            $shipping->states = $shipping->states;
            $shipping->shipping_rates = $shipping->shipping_rates;
            $shipping->customer_groups = $shipping->customer_groups;
            return $shipping;
        } catch (\Exception $e) {
            info($e->getMessage());
            throw new \Exception(SOMETHING_WENT_WRONG);
        }
    }

    public function model()
    {
        return Shipping::class;
    }
}
