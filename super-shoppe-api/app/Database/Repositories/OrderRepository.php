<?php

namespace App\Database\Repositories;

use App\Database\Models\Voucher;
use App\Enums\OrderTypeEnum;
use App\Enums\ProductType;
use App\Enums\ShippingType;
use App\Enums\VoucherStatusEnum;
use App\Services\Api\DelyvaService;
use App\Services\CreditMemoService;
use App\Services\ShippingService;
use App\Traits\Delyva;
use App\Traits\Carts;
use App\Traits\Users;
use App\Traits\IPay88;
use App\Traits\Invoices;
use App\Traits\Shipments;
use App\Traits\OrderHistory;
use App\Enums\OrderEnum;
use App\Database\Models\Cart;
use App\Database\Models\User;
use App\Database\Models\Order;
use App\Database\Models\Branch;
use App\Database\Models\OrderAttribute;
use App\Database\Models\PaymentGateway;
use App\Database\Models\PaymentMethod;
use App\Database\Models\Product;
use App\Database\Models\Shipping;
use App\Database\Models\Variation;
use App\Enums\BranchEnum;
use App\Enums\PaymentEnum;
use App\Enums\RoleEnum;
use App\Enums\WalletActionTypeEnum;
use App\Enums\WalletCurrencyEnum;
use App\Enums\WalletSpendActionEnum;
use App\Enums\WalletTransactionEnum;
use App\Events\OrderInventoryUpdate;
use App\Events\OrderSuccess;
use App\Services\CartService;
use App\Traits\HasVoucherProduct;
use App\Traits\RazerPay;
use Log;
use Prettus\Validator\Exceptions\ValidatorException;

use App\Traits\Wallets;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Marvel\Database\Models\OrderStatus;
use Marvel\Database\Models\Shop;
use Marvel\Database\Repositories\OrderRepository as MarvelOrderRepository;

class OrderRepository extends MarvelOrderRepository
{
    use Carts;
    use Users;
    use IPay88;
    use Wallets;
    use RazerPay;
    use Invoices;
    use Shipments;
    use OrderHistory;
    use HasVoucherProduct;

    protected $fieldSearchable = [
        'type',
        'display_id' => 'like',
        'customer.name' => 'like',
        'customer_contact' => 'like',
        'products.name' => 'like',
        'payment_method_id',
        'shipping_class_id',
        'status.slug',
        'created_at' => 'between',
    ];

    protected $dataArray = [
        'type',
        'display_id',
        'tracking_number',
        'customer_id',
        'customer_contact',
        'customer_email',
        'status',
        'amount',
        'sales_tax',
        'paid_total',
        'total',
        'total_invoiced',
        'total_refunded',
        'shop_id',
        'coupon_id',
        'coupon_discount',
        'discount',
        'points_used',
        'points_discount',
        'golds_used',
        'golds_discount',
        'store_credit',
        'payment_method_id',
        'payment_id',
        'payment_gateway',
        'shipping_address',
        'billing_address',
        'state_id',
        'shipping_class_id',
        'logistics_provider',
        'items_weight',
        'delivery_fee',
        'delivery_fee_quoted',
        'delivery_remarks',
        'delivery_time',
    ];


    protected $cartRepository;
    protected $creditMemoService;
    protected $shippingService;
    protected $cartService;

    protected $delyvaService;

    public function __construct(CartRepository $cartRepository, CreditMemoService $creditMemoService, ShippingService $shippingService, CartService $cartService, DelyvaService $delyvaService)
    {
        parent::__construct(app());
        $this->cartRepository = $cartRepository;
        $this->creditMemoService = $creditMemoService;
        $this->shippingService = $shippingService;
        $this->cartService = $cartService;
        $this->delyvaService = $delyvaService;
    }

    public function model()
    {
        return Order::class;
    }

    public function getAllPaginatedOrders(array $filters): LengthAwarePaginator
    {
        if ($order_search = Arr::get($filters, 'order_search')) {
            $this->where(function ($query) use ($order_search) {
                $query->whereRaw('orders.display_id LIKE ?', ["%{$order_search}%"])
                    ->orWhereHas('payments', fn($q) => $q->whereRaw('payment_transactions.payment_reference_id LIKE ?', ["%{$order_search}%"]));
            });
        }

        if ($product_search = Arr::get($filters, 'product_search')) {
            $this->whereHas('products', function ($query) use ($product_search) {
                $query->where('order_product.product_id', $product_search)
                    ->orWhereRaw('order_product.name LIKE ?', ["%{$product_search}%"])
                    ->orWhereRaw('order_product.sku LIKE ?', ["%{$product_search}%"]);
            });
        }

        if ($customer_search = Arr::get($filters, 'customer_search')) {
            $this->where(function ($query) use ($customer_search) {
                $query->whereRaw('customer_email LIKE ?', ["%{$customer_search}%"])
                    ->orWhereHas('customer', fn($q) => $q->whereRaw('users.name LIKE ?', ["%{$customer_search}%"]));
            });
        }

        $order_from = Arr::get($filters, 'order_from');
        $order_to = Arr::get($filters, 'order_to');
        if ($order_from || $order_to) {
            $this->where(function ($query) use ($order_from, $order_to) {
                $query->when($order_from, fn($q, $date) => $q->where('orders.created_at', '>', Carbon::parse($date)->format('Y-m-d 00:00:00')))
                    ->when($order_to, fn($q, $date) => $q->where('orders.created_at', '<=', Carbon::parse($date)->format('Y-m-d 23:59:59')));
            });
        }

        if ($payment_status = Arr::get($filters, 'payment_status')) {
            $this->whereRelation('payments', 'payment_transactions.status', '=', $payment_status);
        }

        if ($order_status = Arr::get($filters, 'order_status')) {
            $this->whereHas('order_status', fn($q) => $q->where('order_status.slug', $order_status));
        }

        if (Arr::get($filters, 'with_refund')) {
            $this->orHas('refund');
        }

        $orderBy = Arr::get($filters, 'order_by', 'created_at');
        $orderDirection = Arr::get($filters, 'order', 'DESC');

        $this->orderBy($orderBy, $orderDirection);

        $limit = Arr::get($filters, 'limit', 10);
        $page = Arr::get($filters, 'page', 1);
        $paginateType = Arr::get($filters, 'type', null);

        if ($paginateType == "simple") {
            return $this->simplePaginate($limit, ['*'], 'paginate', $page);
        }
        return $this->paginate($limit, ['*'], 'paginate', $page);
    }

    public function hasPermission($user, $shop_id = null)
    {
        if ($user && $user->hasRole(RoleEnum::SUPER_ADMIN)) {
            return true;
        }
        try {
            $shop = Shop::findOrFail($shop_id);
        } catch (\Exception $e) {
            return false;
        }
        if (!$shop->is_active) {
            throw new \Exception(SHOP_NOT_APPROVED);
        }
        if ($user && $user->hasRole(RoleEnum::STORE_OWNER)) {
            if ($shop->owner_id === $user->id) {
                return true;
            }
        } elseif ($user && $user->hasRole(RoleEnum::STAFF)) {
            if ($shop->staffs->contains($user)) {
                return true;
            }
        }
        return false;
    }

    public function placeOrder(Cart $cart)
    {
        $errors = $this->cartRepository->revalidate($cart, true); // force recalculate cart items and totals
        $this->validateCartBeforePlaceOrder($cart, $errors);

        $request = $this->generateOrderPayloadFromCart($cart);
        $order = $this->createOrder(collect($request));
        $response = $this->generatePayment($order);
        $response['applied_rule_ids'] = $cart->applied_rule_ids ?: null;
        $cart->emptyCart(true);

        return $response;
    }

    public function storeOrder($request)
    {
        $payload = $this->generateOrderPayloadFromRequest($request);
        $order = $this->createOrder(collect($payload));
        $this->generatePayment($order, [
            'payment_reference_id' => $request->payment_reference_id,
            'response_message' => $request->payment_remarks,
            'status' => $order->paid_total >= $order->total ? "success" : "pending"
        ]);
        $this->saveAttributes($order, $request);
        return $order;
    }

    protected function validateCartBeforePlaceOrder(Cart $cart, $errors)
    {
        if ($errors && count($errors)) {
            throw new \Exception($errors[0]);
        }
        if ($cart->getIsEmptyAttribute(true)) {
            throw new \Exception("Failed to place order. No items selected.");
        }
        if (!$cart->billing_address || !$cart->billing_address['address'] || !$cart->shipping_address || !$cart->shipping_address['address']) {
            throw new \Exception("Failed to place order. Address not selected.");
        }
        if (!$this->validateAddress($cart->billing_address) || !$this->validateAddress($cart->shipping_address)) {
            throw new \Exception("Failed to place order. Please edit addresses to proceed.");
        }
        if (!$cart->validateAppliedDiscounts()) {
            throw new \Exception("Failed to place order. One or more applied discounts are invalid");
        }
        if (!$this->cartService->validateItems($cart)) {
            throw new \Exception("Failed to place order. One or more items cannot be purchased in the same order");
        }

        if (!$cart->shipping_method || !$this->shippingService->validateShipping($cart, $cart->shipping_method)) {
            throw new \Exception("Failed to place order. Please edit checkout details to proceed.");
        }
        if ($cart->user_id && $cart->points_used > 0) {
            $wallet = $cart->user->wallets()->groupType(WalletCurrencyEnum::POINT)->first();
            if (!$wallet || $wallet->balance < $cart->points_used) {
                throw new \Exception("Failed to place order. Insufficient balance.");
            }
        }
        if ($cart->user_id && $cart->store_credit > 0) {
            $wallet = $cart->user->wallets()->groupType(WalletCurrencyEnum::STORE_CREDIT)->first();
            if (!$wallet || $wallet->balance < $cart->store_credit) {
                throw new \Exception("Failed to place order. Insufficient store credit balance.");
            }
            if ($cart->store_credit < $cart->subtotal && !$cart->payment_method) {
                throw new \Exception("Failed to place order. Payment method is not selected.");
            }
        } else if ($cart->store_credit <= 0 && !$cart->payment_method) {
            throw new \Exception("Failed to place order. Payment method is not selected.");
        }
        if ($cart->shipping_class_id && is_null($this->shippingService->calculateShippingFee($cart, $cart->shipping_class_id))) {
            throw new \Exception("Failed to place order. No shipping service available.");
        }
        $bulkyItemErrors = $cart->validateBulkyItems();
        if ($bulkyItemErrors && isset($bulkyItemErrors['message'])) {
            throw new \Exception($bulkyItemErrors['message']);
        }
    }

    protected function generateOrderPayloadFromCart(Cart $cart)
    {
        $order_status = OrderStatus::firstWhere(['slug' => OrderEnum::PENDING, 'default' => true]);
        $shipping_method = $cart->shipping_method()->first();
        $payment_method = $cart->payment_method()->first();
        $points_used = 0;
        $points_discount = 0;
        $wallet_rule_amount = 0;
        if ($cart->use_points) {
            $redeem_values = $this->cartRepository->calculateCartRedeem($cart);
            if ($redeem_values) {
                $points_used = $redeem_values['redeem_amount'];
                $points_discount = $redeem_values['redeem_value'];
                $wallet_rule_amount = $redeem_values['rule_amount'];
            }
        }

        $remainingPoints = $points_used;
        $cart_items = $cart->items()->wherePivot('is_checkout', true)->get()->map(function ($cart_item) use (&$remainingPoints, $wallet_rule_amount) {
            $variant = $cart_item->pivot->variation_option_id ? Variation::find($cart_item->pivot->variation_option_id) : null;

            $redeemablePoints = min($variant ?
                $variant->max_redeemable_points * $cart_item->pivot->quantity :
                $cart_item->max_redeemable_points * $cart_item->pivot->quantity, $remainingPoints);
            $remainingPoints -= $redeemablePoints;
            $point_discount_amount = $redeemablePoints * $wallet_rule_amount;
            return [
                'product_id' => $cart_item->pivot->product_id,
                'product_type' => $cart_item->product_type,
                'variation_option_id' => $cart_item->pivot->variation_option_id,
                'name' => $variant ? $variant->title : $cart_item->name,
                'sku' => $variant ? $variant->sku : $cart_item->sku,
                'barcode' => $variant ? $variant->barcode : $cart_item->barcode,
                'width' => $variant ? $variant->width : $cart_item->width,
                'height' => $variant ? $variant->height : $cart_item->height,
                'length' => $variant ? $variant->length : $cart_item->length,
                'weight' => $variant ? $variant->weight : $cart_item->weight,
                'image' => $variant
                    ? (($variant->image && isset($variant->image['original']))
                        ? $variant->image['original']
                        : null
                    ) : (($cart_item && $cart_item->image && isset($cart_item->image['original']))
                        ? $cart_item->image['original']
                        : null
                    ),
                'banner' => ($cart_item && $cart_item->banner && isset($cart_item->banner['original']))
                    ? $cart_item->banner['original']
                    : null,
                'order_quantity' => $cart_item->pivot->quantity,
                'unit_price' => $cart_item->pivot->price,
                'tngd_price' => ($variant ? $variant->sale_price : $cart_item->sale_price) ?: 0,
                'subtotal' => $cart_item->pivot->subtotal,
                'max_redeemable_points' => $redeemablePoints,
                'point_discount_amount' => $point_discount_amount
            ];
        });

        $subtotal = $cart->getSubtotalWithShippingAndAppliedDiscounts($cart->items_total - $points_discount);

        $order_request = [
            'type' => $this->getType($cart->items),
            'customer_id' => $cart->user_id,
            'customer_contact' => $cart->billing_address ? $cart->billing_address['address']['contact_number'] : $cart->user->phone,
            'customer_email' => $cart->billing_address ? $cart->billing_address['address']['email'] : $cart->user->email,
            'amount' => $cart->items_total,
            'total' => $subtotal - $cart->store_credit,
            'delivery_fee' => $cart->delivery_fee,
            'delivery_fee_quoted' => $cart->delivery_fee_quoted,
            'delivery_remarks' => $cart->shipping_remarks,
            'items_weight' => $cart->items_weight,
            'sales_tax' => $cart->tax,
            'coupon_discount' => $cart->coupon_discount,
            'discount' => $cart->discount,
            'store_credit' => $cart->user_id ? $cart->store_credit : 0,
            'status' => $order_status->id,
            'shipping_address' => $cart->shipping_address ? $cart->shipping_address['address'] : null,
            'billing_address' => $cart->billing_address ? $cart->billing_address['address'] : null,
            'state_id' => $cart->state_id,
            'shipping_class_id' => $shipping_method ? $shipping_method->id : null,
            'products' => $cart_items
        ];
        if ($cart->use_points && $points_used > 0 && $points_discount > 0) {
            $order_request['points_used'] = $points_used;
            $order_request['points_discount'] = $points_discount;
        }
        if ($payment_method) {
            $order_request = array_merge($order_request, [
                'payment_method_id' => $payment_method->id,
                'payment_id' => $payment_method->code,
                'payment_gateway' => ($payment_method->gateway ? $payment_method->gateway->code : null),
            ]);
        }
        if (isset($cart->store_credit) && $cart->store_credit > 0) {
            $order_request = array_merge($order_request, [
                'payment_method_id' => isset($order_request['payment_method_id']) ? $order_request['payment_method_id'] : null,
                'payment_id' => isset($order_request['payment_id']) ? $order_request['payment_id'] : null,
                'payment_gateway' => isset($order_request['payment_gateway']) ? $order_request['payment_gateway'] : 'storecredit',
            ]);
        }
        return $order_request;
    }

    protected function generateOrderPayloadFromRequest($request)
    {
        $order_status = OrderStatus::firstWhere(['slug' => OrderEnum::PENDING, 'default' => true]);
        $payment_method = $request->payment_method ? PaymentMethod::find($request->payment_method) : null;
        $items_weight = 0;
        $items_total = 0;
        $items = array_map(function ($item) use (&$items_total, &$items_weight) {
            $subtotal = $item['price'] * $item['quantity'];
            $items_total += $subtotal;
            $product = Product::findOrFail($item['id']);
            $variant = $item['variation_option_id'] ? $product->variation_options()->findOrFail($item['variation_option_id']) : null;
            $items_weight += ($variant ? $variant->weight : $product->weight) * $item['quantity'];
            return [
                'product_type' => $product->product_type,
                'product_id' => $item['id'],
                'variation_option_id' => $item['variation_option_id'],
                'name' => $variant ? $variant->title : $product->name,
                'sku' => $variant ? $variant->sku : $product->sku,
                'barcode' => $variant ? $variant->barcode : $product->barcode,
                'width' => $variant ? $variant->width : $product->width,
                'height' => $variant ? $variant->height : $product->height,
                'length' => $variant ? $variant->length : $product->length,
                'weight' => $variant ? $variant->weight : $product->weight,
                'image' => $variant
                    ? (($variant->image && isset($variant->image['original']))
                        ? $variant->image['original']
                        : null
                    ) : (($product && $product->image && isset($product->image['original']))
                        ? $product->image['original']
                        : null
                    ),
                'banner' => ($product && $product->banner && isset($product->banner['original']))
                    ? $product->banner['original']
                    : null,
                'order_quantity' => $item['quantity'],
                'unit_price' => $item['price'],
                'subtotal' => $subtotal,
                'max_redeemable_points' => $variant ? $variant->max_redeemable_points : $product->max_redeemable_points
            ];
        }, $request->products);

        $subtotal = $items_total + $request->delivery_fee - $request->discount;
        $order_request = [
            'type' => $this->getType($items),
            'customer_id' => $request->customer['id'],
            'customer_contact' => $request->customer['phone'],
            'customer_email' => $request->customer['email'],
            'amount' => $items_total,
            'total' => $subtotal,
            'delivery_fee' => $request->delivery_fee,
            'delivery_remarks' => $request->delivery_remarks,
            'items_weight' => $items_weight,
            // 'sales_tax' => $cart->tax,
            'discount' => $request->discount,
            // 'store_credit' => $cart->user_id ? $cart->store_credit : 0,
            'status' => $order_status->id,
            'shipping_address' => $request->shipping_address,
            'billing_address' => $request->billing_address,
            'state_id' => $request->state_id,
            'shipping_class_id' => null,
            'paid_total' => $request->paid_total,
            'products' => $items,
            'comment' => isset($request->comment) ? $request->comment : null
        ];
        // if($cart->use_points && $points_used > 0 && $points_discount > 0) {
        //     $order_request['points_used'] = $points_used;
        //     $order_request['points_discount'] = $points_discount;
        // }
        if ($payment_method) {
            $order_request = array_merge($order_request, [
                'payment_method_id' => $payment_method->id,
                'payment_id' => $payment_method->code,
                'payment_gateway' => ($payment_method->gateway ? $payment_method->gateway->code : null),
            ]);
        }
        // if(isset($request->store_credit) && $request->store_credit > 0) {
        //     $order_request = array_merge($order_request, [
        //         'payment_method_id' => isset($order_request['payment_method_id']) ? $order_request['payment_method_id'] : null,
        //         'payment_id' => isset($order_request['payment_id']) ? $order_request['payment_id'] : null,
        //         'payment_gateway' => isset($order_request['payment_gateway']) ? $order_request['payment_gateway'] : 'storecredit',
        //     ]);
        // }
        return $order_request;
    }

    protected function generatePayment(Order $order, $payment_details = [])
    {
        $payment = null;
        if ($order->total > 0) {
            // If payment with Cash on Delivery
            if ($order->payment_id === "cod" || $order->payment_id === "cashondelivery") {
                $this->orderSuccess($order);
            }
            $payment = $order->payments()->create(array_merge([
                'customer_id' => $order->customer_id,
                'payment_id' => $order->payment_method()->first()->code,
                'payment_method_id' => $order->payment_method_id,
                'amount' => $order->total,
                'customer_contact' => $order->billing_address['contact_number'],
                'customer_email' => $order->billing_address['email']
            ], $payment_details));
            $redirect_url = '/checkout/payment';
        } else {
            $paid_status = OrderStatus::firstWhere('slug', OrderEnum::PROCESSING);

            $is_voucher_checkout = $this->isAllVoucherCheckout($order->products);
            if ($is_voucher_checkout) {
                $paid_status = OrderStatus::firstWhere('slug', OrderEnum::COMPLETE);
            }

            $order->fill([
                'paid_total' => $order->total,
                'status' => $paid_status->id
            ])->save();
            $this->createHistory($order, ['order_status_id' => $paid_status->id]);
            OrderSuccess::dispatch($order);
            $redirect_url = '/checkout/result';
        }
        return [
            'success' => true,
            'order' => $order,
            'payment' => $payment,
            'redirect_url' => $redirect_url
        ];
    }

    public function shipment(Order $order)
    {
        if (!$order->shipping_class_id) {
            return;
        }

        if ($this->isAllVoucherCheckout($order->products)) {
            return;
        }

        $shipping = Shipping::withoutGlobalScope('group_scope')->find($order->shipping_class_id);

        if ($shipping->type != ShippingType::DELYVA) {
            return;
        }

        // Create shipments per product group
        $this->delyvaService->createShipmentsPerGroup($order);
    }

    public function storeInvoice(Order $order, $request)
    {
        if (!$this->canCreateMoreInvoice($order)) {
            return $order->invoices;
        }

        // create invoice from order data
        $invoice = $this->createOrderInvoice($order, $request);

        $order_status = OrderStatus::firstWhere(['slug' => OrderEnum::PROCESSING, 'default' => true]);
        if ($order->status < $order_status->id) { // update order status if status is still 'pending'
            $order->fill([
                'total_invoiced' => $invoice->grand_total,
                'paid_total' => $invoice->grand_total > $order->paid_total ? $invoice->grand_total : $order->paid_total,
                'status' => $order_status->id
            ])->save();

            $this->createHistory($order, [
                'order_status_id' => $order_status->id,
                'require_email_notification' => false
            ]);
        } else {
            $order->fill([
                'total_invoiced' => $invoice->grand_total,
                'paid_total' => $invoice->grand_total > $order->paid_total ? $invoice->grand_total : $order->paid_total,
            ])->save();
        }

        return $invoice;
    }

    public function storeShipment(Order $order, $request)
    {
        $shipment = $this->createOrderShipment($order, $request);

        $order_status = OrderStatus::firstWhere(['slug' => OrderEnum::IN_DELIVERY, 'default' => true]);
        if ($order->status < $order_status->id) { // update order status if status is still 'packed' or 'processing
            $order->fill([
                'status' => $order_status->id
            ])->save();

            $this->createHistory($order, [
                'order_status_id' => $order_status->id,
                'require_email_notification' => false
            ]);
        }

        return $shipment;
    }

    public function storeCreditMemo(Order $order, $request)
    {
        $credit_memo = $this->creditMemoService->createOrderCreditMemo($order, $request);

        $this->creditMemoService->processDirectRefund($order, $credit_memo);

        $this->creditMemoService->processPointRefund($order, $credit_memo);

        return $credit_memo;
    }

    public function saveAttributes($order, $request)
    {
        $attribute_request = [];
        foreach ($request->input('attributes') as $_attribute) {
            $attribute = null;
            if (isset($_attribute['id'])) {
                $attribute = OrderAttribute::find($_attribute['id']);
            }
            if (!$attribute) {
                $attribute = OrderAttribute::firstOrCreate([
                    'title' => $_attribute['title']
                ]);
            }
            $attribute_request[$attribute->id] = [
                'value' => $_attribute['value']
            ];
        }
        $order->attributes()->sync($attribute_request);
    }

    public function groupOrdersByStatus(User $user = null)
    {
        if ($user) {
            return OrderStatus::withCount(['orders' => fn($query) => $query->where('orders.customer_id', $user->id)])->get();
        }
        return OrderStatus::withCount(['orders'])->get();
    }

    public function groupProducts(Request $request, User $user = null)
    {
        $limit = isset($request->limit) ? $request->limit : 20;
        $page = isset($request->page) ? $request->page : 1;
        if ($user) {
            return Product::with(['type', 'tier_prices', 'inventory', 'tags', 'variations.attribute', 'variation_options.inventory', 'variation_options.tier_prices'])->withCount(['orders' => fn($query) => $query->where('orders.customer_id', $user->id)])->having('orders_count', '>', 0)->paginate($limit, ['*'], 'page', $page);
        }
        return Product::with(['type', 'tier_prices', 'inventory', 'tags', 'variations.attribute', 'variation_options.inventory', 'variation_options.tier_prices'])->withCount('orders')->having('orders_count', '>', 0)->paginate($limit, ['*'], 'page', $page);
    }

    public function checkPayment(Cart $cart)
    {
        // $branch_id = Branch::getId();
        // $branch = Branch::find($branch_id ?: 1);
        // if(!$branch) {
        //     $branch = Branch::find(1);
        // }
        // if($branch->code === BranchEnum::TNG) {
        // }
        $this->presetTNGPaymentMethod($cart);
    }

    public function getDelyvaPrintLabelUrl(Order $order)
    {
        $delyvaService = app(DelyvaService::class);
        return $delyvaService->getAirwayBillURL($order);
    }

    /**
     * Get AWB URLs for all product groups
     * @param Order $order
     * @return array
     */
    public function getDelyvaPrintLabelUrlsPerGroup(Order $order)
    {
        $delyvaService = app(DelyvaService::class);
        return $delyvaService->getAirwayBillURLsPerGroup($order);
    }

    protected function presetTNGPaymentMethod(Cart $cart)
    {
        $payment_gateway = PaymentGateway::firstWhere('code', PaymentEnum::TNG_GATEWAY_CODE);
        $payment_method = $payment_gateway->payment_methods()->first();
        $cart->payment_method_id = $payment_method->id;
        $cart->save();
    }

    protected function createOrder($request)
    {
        try {
            $orderInput = $request->only($this->dataArray);
            $products = $this->processProductsV2($request['products']);

            $order = $this->create($orderInput->all());
            if (isset($request['branch'])) {
                $order->branch()->sync([$request['branch']]);
            }

            $order->products()->createMany($products);
            $this->createOrderCreatedHistory($order, isset($request['comment']) ? $request['comment'] : null);
            $this->calculateShopIncome($order);

            if (isset($request['store_credit']) && $request['store_credit'] > 0) {
                $this->processStoreCreditPayment($order, $request['store_credit']);
            }

            return $this->find($order->id);
        } catch (ValidatorException $e) {
            throw new \Exception(SOMETHING_WENT_WRONG);
        }
    }

    protected function processProductsV2($products)
    {
        foreach ($products as $key => $product) {
            if (!isset($product['variation_option_id'])) {
                $product['variation_option_id'] = null;
                $products[$key] = $product;
            }
        }
        return $products;
    }

    protected function processStoreCreditPayment($order, $credit_used)
    {
        $total_credits_spent = abs($order->wallet_transactions()->creditSpendInvoice()->get()->sum('amount')); // parse to positive value
        if (($credit_used - $total_credits_spent) > 0) {
            $user = $order->customer;
            $wallet = $user->wallets()->groupType(WalletCurrencyEnum::STORE_CREDIT)->first();
            if ($wallet) {
                $order->wallet_transactions()->create([
                    'wallet_id' => $wallet->id,
                    'customer_id' => $user->id,
                    'title' => 'Spend credits for purchasing order #' . $order->display_id,
                    'action' => WalletSpendActionEnum::CART,
                    'action_type' => WalletActionTypeEnum::SPEND,
                    'currency' => WalletCurrencyEnum::STORE_CREDIT,
                    'amount' => - ($credit_used - $total_credits_spent),
                    'status' => WalletTransactionEnum::COMPLETED
                ]);
            }
        }
    }

    public function createOrderDiscountUsage(Order $order, $cart_applied_rules)
    {
        $user = $order->customer;
        $discounts = array_map(function ($discount_id) use ($user, $order) {
            return [
                'discount_id' => $discount_id,
                'customer_id' => $user ? $user->id : null,
                'order_status' => $order->status,
            ];
        }, $cart_applied_rules);
        $order->discounts()->attach($discounts);
    }

    public function generateTransactionResponseMessage($gateway, $payload, $extras = [])
    {
        $message = 'Payment Via: ' . $gateway->title . "\r\n";
        pretty_dump($message, $payload);
        pretty_dump($message, $extras);
        return $message;
    }

    public function markOrderCompletedIfOnlyVoucherOrder(Order $order)
    {
        if (!$this->isAllVoucherCheckout($order->products)) {
            return;
        }

        if ($order->hasStatus(OrderEnum::COMPLETE)) {
            return;
        }

        $status = OrderStatus::firstWhere('slug', OrderEnum::COMPLETE);
        $order->fill([
            'status' => $status->id
        ])->save();
        $this->createHistory($order, ['order_status_id' => $status->id]);
    }

    private function getType($items)
    {
        // if ($this->isVoucherCheckout($items)) {
        //     return OrderTypeEnum::VOUCHER;
        // }

        return OrderTypeEnum::SIMPLE;
    }
}
