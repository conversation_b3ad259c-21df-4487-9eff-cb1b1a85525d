<?php

namespace App\Services;

use App\Database\Models\Cart;
use App\Database\Models\MembershipTier;
use App\Database\Models\Shipping;
use App\Enums\ShippingType;
use App\Services\Api\DelyvaService;
use App\Traits\Carts;
use Illuminate\Support\Arr;


class ShippingService
{
    use Carts;

    protected DelyvaService $delyvaService;

    public function __construct(DelyvaService $delyvaService)
    {
        $this->delyvaService = $delyvaService;
    }

    public function calculateShippingFee(Cart $cart, $shipping_class_id)
    {
        $shipping = Shipping::findOrFail($shipping_class_id);

        if (!$this->validateShipping($cart, $shipping)) {
            return null;
        }

        // Try group-based calculation first
        $groupShippingFees = $this->calculateShippingFeePerGroup($cart, $shipping_class_id);

        if (!empty($groupShippingFees)) {
            // Calculate total shipping fee from all groups
            $totalShippingFee = 0;
            $totalQuotedFee = 0;
            $hasErrors = false;

            foreach ($groupShippingFees as $groupFee) {
                if (isset($groupFee['error'])) {
                    $hasErrors = true;
                    break;
                }
                $totalShippingFee += $groupFee['shipping_fee'] ?? 0;
                $totalQuotedFee += $groupFee['quoted_fee'] ?? 0;
            }

            if (!$hasErrors) {
                $cart->shipping_class_id = $shipping_class_id;
                $cart->delivery_fee = $totalShippingFee;
                $cart->delivery_fee_quoted = $totalQuotedFee;
                $cart->save();

                return [
                    'shipping_fee' => $totalShippingFee,
                    'quoted_fee' => $totalQuotedFee,
                    'group_fees' => $groupShippingFees
                ];
            }
        }

        // Fallback to original calculation
        $fee = $this->getShippingValue($shipping, $cart);

        if ($fee !== null) {
            $cart->shipping_class_id = $shipping_class_id;
            if (is_array($fee)) {
                $cart->delivery_fee = $fee['shipping_fee'];
                $cart->delivery_fee_quoted = $fee['quoted_fee'];
            } else {
                $cart->delivery_fee = $fee;
            }
            $cart->save();
        }
        return $fee;
    }

    public function getShippingValue(Shipping $shipping, Cart $cart): float | array | null
    {

        $items = $cart->items()->wherePivot('is_checkout', true)->get();

        if ($cart->isAllVoucherCheckout($items)) {
            return 0;
        }

        if (!$this->checkShippingApplicableProducts($shipping, $items)) {
            return null;
        }

        // check cart selected state and shipping zone
        if (!$this->checkShippingApplicableZones($shipping, $cart)) {
            return null;
        }

        switch ($shipping->type) {
            case ShippingType::DELYVA:
                $resp = $this->delyvaService->getQuote($cart, $shipping);

                if ($resp['status'] != 200) {
                    return null;
                    // throw new \Exception($resp['message']);
                }
                $quoted_price = $resp['price'];
                $tiered_fee = $this->calculateTieredFee($shipping, $cart);
                $force_use_tier_amount = config('shop.shipping_use_tier_amt');
                // use tier amount if configured **this config must be false in Production
                return [
                    'quoted_fee' => $quoted_price,
                    'shipping_fee' => $tiered_fee ? (($force_use_tier_amount || $tiered_fee > $quoted_price) ? $tiered_fee : $quoted_price) : $quoted_price
                ];
            case ShippingType::DYNAMIC:
                return $this->calculateDynamicFee($shipping, $cart);
                break;
            case ShippingType::FIXED:
                return $shipping->amount;
                break;
            case ShippingType::PERCENTAGE:
                return $this->calculatePercentageFee($shipping, $cart->subtotal, $cart->delivery_fee);
                break;
            case ShippingType::FREE:
                return 0;
                break;
            default:
                return null;
        }
    }

    public function validateShipping(Cart $cart, Shipping $shipping)
    {
        if (!$shipping->is_active) {
            return false;
        }
        if ($shipping->shipping_rule) {
            $shipping_rule = $shipping->shipping_rule;

            $user = auth()->user();

            if (count($shipping_rule->membership_tiers)) {
                $membership_tier = $user && $user->user_membership_tier ? $user->user_membership_tier->membershipTier : MembershipTier::where('rank', 1)->first();
                if (!$membership_tier) {
                    return false;
                }
                $applicable_shipping_rule_tier = $shipping_rule->membership_tiers->firstWhere('id', $membership_tier->id);
                if ($applicable_shipping_rule_tier) {
                    $shipping_rule = $applicable_shipping_rule_tier->pivot;
                }
            }

            $amount = null;
            if ($shipping_rule->type === 'amount')
                $amount = $cart->subtotal - $cart->delivery_fee;
            if ($shipping_rule->type === 'weight')
                $amount = $cart->items_weight;
            if ($shipping_rule->type === 'quantity')
                $amount = $cart->items_qty;
            if ($amount === null) {
                return false;
            }
            if (
                ($shipping_rule->min_amount && $amount < $shipping_rule->min_amount) ||
                ($shipping_rule->max_amount && $amount > $shipping_rule->max_amount)
            ) {
                return false;
            }

            if (!$this->checkShippingApplicableProducts($shipping, $cart->items()->wherePivot('is_checkout', true)->get())) {
                return false;
            }
        }
        return true;
    }

    public function checkShippingApplicableProducts(Shipping $shipping, $products)
    {
        $shippingRule = $shipping->shipping_rule;
        if (!$shippingRule) {
            return true;
        }

        $shippingProducts = $shippingRule->products()->withoutGlobalScope('status_permission')->get();
        if ($shippingProducts->count() < 1) {
            return true;
        }

        $checkoutProductIds = $products->pluck('id')->toArray();

        $applicableProductIds = $shippingProducts->filter(fn($p) => $p->pivot->is_applicable)->pluck('id')->toArray();
        if (count($applicableProductIds) > 0) {
            // has applicable products set, check for any missing checkout items eligible for this shipping
            $nonEligibleProductIds = array_diff($checkoutProductIds, $applicableProductIds);
            if (count($nonEligibleProductIds) > 0) {
                return false;
            }
        }

        $nonApplicableProductIds = $shippingProducts->filter(fn($p) => !$p->pivot->is_applicable)->pluck('id')->toArray();
        if (count($nonApplicableProductIds) > 0) {
            // has non-applicable products set, check for any checkout items not eligible for this shipping
            $eligibleProductIds = array_diff($checkoutProductIds, $nonApplicableProductIds);
            if (count($eligibleProductIds) < count($checkoutProductIds)) {
                return false;
            }
        }

        return true;
    }

    public function checkShippingApplicableZones(Shipping $shipping, Cart $cart)
    {
        $withZonesShippingRates = $shipping->shipping_rates()
            ->whereNotNull('shipping_zone_id')
            ->get();

        // no specific shipping zones configured for this shipping
        if ($withZonesShippingRates->count() < 1) {
            return true;
        }

        if (!$cart->state) {
            return false;
        }

        $applicableRate = $withZonesShippingRates->whereIn('shipping_zone_id', $cart->state->shipping_zones->pluck('id'))->first();

        return !is_null($applicableRate);
    }

    public function calculateDynamicFee(Shipping $shipping, Cart $cart)
    {
        $items_weight = $this->calculateCartWeight($cart);

        $shipping_rate = $shipping->shipping_rates()->where(
            function ($query) use ($items_weight) {
                $query->where('weight_from', '<=', $items_weight);
                $query->where('weight_to', '>=', $items_weight);
            }
        )->first();

        // no rate found - get the highest one because exceeded maximum weight
        if (!$shipping_rate) {
            $items_weight = $shipping->shipping_rates()->max('weight_to');
            $shipping_rate = $shipping->shipping_rates()->where(
                function ($query) use ($items_weight) {
                    $query->where('weight_from', '<=', $items_weight);
                    $query->where('weight_to', '>=', $items_weight);
                }
            )->first();
        }

        return $shipping_rate->amount;
    }

    public function calculatePercentageFee(Shipping $shipping, $subtotal, $existing_delivery_fee)
    {
        return ($subtotal - $existing_delivery_fee) * ($shipping->amount / 100);
    }

    public function calculateTieredFee(Shipping $shipping, Cart $cart)
    {
        if (!$cart->state) {
            return null;
        }

        $shipping_zones = $cart->state->shipping_zones;

        $shipping_rates = $shipping->shipping_rates()
            ->whereIn('shipping_zone_id', $shipping_zones->pluck('id'))
            ->orderBy('weight_from', 'ASC')
            ->get();

        $base_rate = $shipping_rates->whereNull('weight_increment')->first();
        if (!$base_rate) {
            return null;
        }

        $items_weight = $this->calculateCartWeight($cart);

        if (is_null($base_rate->weight_to) || $items_weight <= $base_rate->weight_to) {
            return $base_rate->amount;
        }

        $applicable_tier_rate = $shipping_rates->whereNotNull('weight_increment')->last(function ($rate) use ($items_weight) {
            return $rate->weight_from <= $items_weight;
        });

        $fee = $base_rate->amount;

        // no applicable rate - return base amount
        if (!$applicable_tier_rate) {
            return $fee;
        }

        $accumulated_weight = $applicable_tier_rate->weight_from;

        while ($accumulated_weight < $items_weight) {
            $accumulated_weight += $applicable_tier_rate->weight_increment;
            $fee += $applicable_tier_rate->amount_increment;
        }

        return $fee;
    }

    /**
     * Calculate shipping fees per product group
     * @param Cart $cart
     * @param int $shipping_class_id
     * @return array
     */
    public function calculateShippingFeePerGroup(Cart $cart, $shipping_class_id)
    {
        $shipping = Shipping::findOrFail($shipping_class_id);

        if (!$this->validateShipping($cart, $shipping)) {
            return [];
        }

        $productGroups = $this->groupItemsByProductGroup($cart);
        $groupShippingFees = [];

        foreach ($productGroups as $groupName => $products) {
            // Create a temporary cart with only products from this group
            $tempCart = $this->createTempCartForGroup($cart, $products);

            if ($shipping->type === ShippingType::DELYVA) {
                $resp = $this->delyvaService->getQuoteForGroup($tempCart, $shipping, $products);

                if ($resp['status'] == 200) {
                    $quoted_price = $resp['price'];
                    $tiered_fee = $this->calculateTieredFee($shipping, $tempCart);
                    $force_use_tier_amount = config('shop.shipping_use_tier_amt');

                    $groupShippingFees[$groupName] = [
                        'quoted_fee' => $quoted_price,
                        'shipping_fee' => $tiered_fee ? (($force_use_tier_amount || $tiered_fee > $quoted_price) ? $tiered_fee : $quoted_price) : $quoted_price,
                        'service_code' => $resp['service_code']
                    ];
                } else {
                    $groupShippingFees[$groupName] = [
                        'error' => $resp['message']
                    ];
                }
            } else {
                // For non-Delyva shipping, calculate based on group weight/value
                $fee = $this->getShippingValueForGroup($shipping, $tempCart);
                $groupShippingFees[$groupName] = [
                    'shipping_fee' => $fee,
                    'quoted_fee' => $fee
                ];
            }
        }

        return $groupShippingFees;
    }

    /**
     * Create a temporary cart object for a specific product group
     */
    private function createTempCartForGroup(Cart $originalCart, $products)
    {
        $tempCart = new Cart();
        $tempCart->shipping_address = $originalCart->shipping_address;
        $tempCart->state_id = $originalCart->state_id;
        $tempCart->state = $originalCart->state;

        // Calculate group-specific totals
        $subtotal = 0;
        $weight = 0;
        $qty = 0;

        foreach ($products as $product) {
            $subtotal += $product->pivot->price_total;
            $item = $product->pivot->variation_option_id ?
                $product->variation_options()->firstWhere('id', $product->pivot->variation_option_id) :
                $product;
            $weight += ($item ? ($item->weight ?: 0) : 0) * $product->pivot->quantity;
            $qty += $product->pivot->quantity;
        }

        $tempCart->subtotal = $subtotal;
        $tempCart->items_weight = $weight;
        $tempCart->items_qty = $qty;

        return $tempCart;
    }

    /**
     * Get shipping value for a specific group
     */
    public function getShippingValueForGroup(Shipping $shipping, Cart $tempCart)
    {
        switch ($shipping->type) {
            case ShippingType::DYNAMIC:
                return $this->calculateDynamicFee($shipping, $tempCart);
            case ShippingType::FIXED:
                return $shipping->amount;
            case ShippingType::PERCENTAGE:
                return $this->calculatePercentageFee($shipping, $tempCart->subtotal, 0);
            case ShippingType::FREE:
                return 0;
            default:
                return null;
        }
    }

    /**
     * Group cart items by product group name
     * @param Cart $cart
     * @return array
     */
    private function groupItemsByProductGroup(Cart $cart)
    {
        $products = $cart->items()->wherePivot('is_checkout', true)->get();
        $groups = [];

        foreach ($products as $product) {
            $group_name = Arr::get($product->meta ?: [], 'product_group_name');
            // If no group name, treat as 'ungrouped'
            $group_key = $group_name ?: 'ungrouped';

            if (!isset($groups[$group_key])) {
                $groups[$group_key] = [];
            }
            $groups[$group_key][] = $product;
        }

        return $groups;
    }
}
