<?php

namespace App\Services;

use App\Database\Models\Cart;
use App\Database\Repositories\CartRepository;
use Illuminate\Support\Arr;

class CartService
{
    protected CartRepository $cartRepository;

    public function __construct(CartRepository $cartRepository)
    {
        $this->cartRepository = $cartRepository;
    }

    public function validateItems(Cart $cart)
    {
        $products = $cart->items()->wherePivot('is_checkout', true)->get();
        if ($products->count() < 1) {
            return true;
        }

        // Allow mixed product groups - AWB will be generated per group
        return true;
    }

    /**
     * Group cart items by product group name
     * @param Cart $cart
     * @return array
     */
    public function groupItemsByProductGroup(Cart $cart)
    {
        $products = $cart->items()->wherePivot('is_checkout', true)->get();
        $groups = [];

        foreach ($products as $product) {
            $group_name = Arr::get($product->meta ?: [], 'product_group_name');
            // If no group name, treat as 'ungrouped'
            $group_key = $group_name ?: 'ungrouped';

            if (!isset($groups[$group_key])) {
                $groups[$group_key] = [];
            }
            $groups[$group_key][] = $product;
        }

        return $groups;
    }
}
