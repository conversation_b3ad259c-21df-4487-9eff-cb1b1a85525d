<?php

namespace App\Services;

use App\Database\Models\Cart;
use App\Database\Repositories\CartRepository;
use Illuminate\Support\Arr;

class CartService
{
    protected CartRepository $cartRepository;

    public function __construct(CartRepository $cartRepository)
    {
        $this->cartRepository = $cartRepository;
    }

    public function validateItems(Cart $cart)
    {
        $products = $cart->items()->wherePivot('is_checkout', true)->get();
        if ($products->count() < 1) {
            return true;
        }

        // initialize with first product group name
        $group = Arr::get($products->first()->meta, 'product_group_name');

        foreach ($products as $product) {
            $current_group = Arr::get($product->meta ?: [], 'product_group_name');
            // different group name - not allow to checkout
            if ($current_group != $group) {
                return false;
            }
        }
        return true;
    }
}