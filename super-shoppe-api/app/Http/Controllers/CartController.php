<?php

namespace App\Http\Controllers;

use App\Database\Models\Order;
use App\Http\Requests\CartUpdateRequest;
use App\Database\Repositories\CartRepository;
use App\Http\Requests\CartBuyAgainRequest;
use Illuminate\Http\Request;
use Marvel\Http\Controllers\CoreController;

class CartController extends CoreController
{
    public $repository;

    public function __construct(CartRepository $repository)
    {
        $this->repository = $repository;
    }

    public function show(Request $request)
    {
        $cart = $this->repository->getOrCreateCart($request);
        $cart->point_info = $this->repository->getPointInfoAttribute($cart);
        $cart->free_shipping_info = $this->repository->getFreeShippingInfoAttribute($cart);
        return $cart;
    }

    public function update(CartUpdateRequest $request)
    {
        return $this->updateCartItems($request);
    }

    public function buyAgain(CartBuyAgainRequest $request)
    {
        $order = Order::findOrFail($request->order_id);
        $items = array_map(function ($item) {
            return [
                'id' => $item['product_id'],
                'variation_option_id' => $item['variation_option_id'],
                'quantity' => $item['order_quantity'],
                'checkout' => true
            ];
        }, $order->products->toArray());
        $request->merge(['items' => $items]);
        return $this->updateCartItems($request);
    }

    protected function updateCartItems($request)
    {
        if (count($request->items) > 0) {
            $cart = $this->repository->getCart($request);

            if (!$cart) {
                throw new \Exception("Failed to update cart");
            }
            $errors = $this->repository->processCartItems($cart, $request->items);
            if (count($errors) > 0) {
                return [
                    'success' => 0,
                    'message' => 'Error occurred when updating cart',
                    'errors' => $errors
                ];
            }
        }

        return [
            'success' => 1,
            'message' => 'Successfully updated cart'
        ];
    }
}
