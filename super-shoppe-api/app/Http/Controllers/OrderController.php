<?php

namespace App\Http\Controllers;

use App\Enums\OrderEnum;
use App\Database\Models\Order;
use App\Database\Models\PaymentMethod;
use App\Database\Models\PaymentGateway;
use App\Database\Repositories\OrderRepository;
use App\Enums\BranchEnum;
use App\Enums\ExportEnum;
use App\Enums\PaymentEnum;
use App\Enums\PaymentTransactionEnum;
use App\Enums\RoleEnum;
use App\Events\OrderSuccess;
use App\Helper\ExportHelper;
use App\Http\Requests\IPay88CallbackRequest;
use App\Http\Requests\OrderCreateRequest;
use App\Http\Requests\OrderExportRequest;
use App\Http\Requests\TNGCallbackRequest;
use App\Http\Requests\OrderRefundRequest;
use App\Http\Requests\OrderShipRequest;
use App\Http\Requests\OrderUpdateAttributesRequest;
use App\Http\Requests\OrderUpdateRequest;
use App\Http\Requests\RazerPayCallbackRequest;
use App\Services\Api\TNGService;
use App\Services\OrderService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Redirect;
use Marvel\Database\Models\OrderStatus;
use Marvel\Http\Controllers\OrderController as MarvelOrderController;

class OrderController extends MarvelOrderController
{
    public $repository;
    protected OrderService $orderService;
    protected TNGService $tngService;

    public function __construct(OrderRepository $repository, OrderService $orderService, TNGService $tngService)
    {
        $this->repository = $repository;
        $this->orderService = $orderService;
        $this->tngService = $tngService;
    }

    public function index(Request $request)
    {
        $input = $request->all();
        return $this->orderService->setUser($request->user())->getPaginatedOrders($input);
    }

    public function fetchOrders(Request $request)
    {
        $this->repository->with('children');
        $user = $request->user();
        if ($user && !$user->hasRole(RoleEnum::CUSTOMER) && (!isset($request->shop_id) || $request->shop_id === 'undefined')) {
            if (!$user->can('view-order')) {
                throw new \Exception(NOT_AUTHORIZED);
            }
            $this->repository->where('id', '!=', null)->where('parent_id', '=', null);
        } else if ($this->repository->hasPermission($user, $request->shop_id)) {
            if ($user && $user->hasRole(RoleEnum::STORE_OWNER)) {
                $this->repository->where('shop_id', '=', $request->shop_id)->where('parent_id', '!=', null);
            } elseif ($user && $user->hasRole(RoleEnum::STAFF)) {
                $this->repository->where('shop_id', '=', $request->shop_id)->where('parent_id', '!=', null);
            }
        } else {
            $this->repository->where('customer_id', '=', $user->id)->where('parent_id', '=', null);
        }
        $this->repository->when($request->with_refund, function ($query) {
            $query->orHas('refund');
        });
        if ($user && !$user->hasRole(RoleEnum::CUSTOMER)) {
            return $this->repository->with(['products.inventory.locations', 'invoices.products']);
        }
        return $this->repository;
    }

    public function overview()
    {
        $user = auth('sanctum')->user();
        if ($user && $user->hasRole(RoleEnum::CUSTOMER)) {
            return $this->repository->groupOrdersByStatus($user);
        }
        return $this->repository->groupOrdersByStatus();
    }

    public function products(Request $request)
    {
        $user = auth('sanctum')->user();
        if ($user && $user->hasRole(RoleEnum::CUSTOMER)) {
            return $this->repository->groupProducts($request, $user);
        }
        return $this->repository->groupProducts($request);
    }

    public function show(Request $request, $id)
    {
        $user = auth('sanctum')->user();
        try {
            $order = $this->repository->with(['customer', 'products.coupons', 'status', 'wallet_transactions', 'payments', 'payment_method', 'shipping_method', 'children.shop', 'history', 'refund', 'attributes', 'discounts.discountable'])->findOrFail($id);
        } catch (\Exception $e) {
            throw new \Exception(NOT_FOUND);
        }
        if ($user && !$user->hasRole(RoleEnum::CUSTOMER)) {
            if (!$user->can('view-order')) {
                throw new \Exception(NOT_AUTHORIZED);
            }
            return $order->loadMissing(['products.coupons', 'products.inventory.locations', 'invoices', 'shipments', 'credit_memos', 'branch', 'tng_loyalty_point_history']);
        }
        if ($user && $user->id === $order->customer_id) {
            return $order;
        } elseif (!$user && $order->customer_id == null) {
            return $order;
        } else {
            throw new \Exception(NOT_AUTHORIZED);
        }
    }

    public function track(Request $request, $order_number)
    {
        $order = $this->repository->withoutBranch()->with(['customer', 'status', 'wallet_transactions', 'payments', 'payment_method', 'shipping_method', 'children.shop', 'invoices', 'shipments', 'credit_memos', 'history', 'refund', 'attributes', 'discounts.discountable'])->firstWhere('display_id', $order_number);
        if (!$order) {
            throw new \Exception(NOT_FOUND);
        }
        return $order;
    }

    public function export(OrderExportRequest $request)
    {
        $exporter = new ExportHelper(ExportEnum::ORDER, "exports", $request->only('type'));
        if (isset($request->from_date)) {
            $exporter->setFrom($request->from_date);
        }
        if (isset($request->to_date)) {
            $exporter->setTo($request->to_date);
        }

        try {
            $result = $exporter->export();
            if ($result) {
                return [
                    'success' => true,
                    'url' => $result
                ];
            } else {
                return [
                    'success' => false
                ];
            }
        } catch (\Exception $e) {
            throw new \Exception('Failed to perform export');
        }
    }

    public function store(Request $request)
    {
        $request = app(OrderCreateRequest::class);
        return $this->repository->storeOrder($request);
    }

    public function save(Request $request)
    {
        $cart = $this->repository->getCart($request);
        if ($cart) {
            $this->repository->checkPayment($cart);

            $create_account_after_order = !$cart->user_id && isset($request->register) && $request->register == true;
            if ($create_account_after_order) {
                $request->validate([
                    'verify_type' => ['sometimes', 'in:email,phone'],
                    'token' => ['required', 'string'],
                    'name' => ['required', 'string', 'max:255'],
                    'email' => ['required_if:phone,null', 'nullable', 'email'],
                    'phone' => ['required_if:email,null', 'nullable'],
                    'dob' => ['sometimes', 'date', 'before:today'],
                    'gender' => ['sometimes', 'string', 'in:male,female'],
                    'password' => ['required', 'string']
                ]);
                if ($this->repository->isUserExisted($request->email, $request->phone)) {
                    throw new \Exception('Email or phone has already been taken');
                }
                $verify_type = isset($request->verify_type) ? $request->verify_type : 'email'; // set to email (old version)
                $verify_value = $verify_type == 'phone' ? remapPhoneNumber($request->phone) : remapEmail($request->email);
                $tokenData = $this->repository->getUserVerifyToken($verify_type, $verify_value, $request->token, $verify_type);
                if (!$tokenData) {
                    throw new \Exception('not_verified');
                }
                if (isset($request->referral_code)) {
                    if (!$this->repository->validateReferralCode($request->referral_code)) {
                        throw new \Exception('Invalid code');
                    }
                }
            }

            $payload = $this->repository->placeOrder($cart);

            if ($payload['success'] && $create_account_after_order) {
                $roles = [RoleEnum::CUSTOMER];
                $addresses = array_unique(array_map(function ($address) {
                    if ($address['id']) {
                        unset($address['id']);
                        return $address;
                    }
                    return $address;
                }, [$cart->billing_address, $cart->shipping_address]), SORT_REGULAR);

                $tokenData->verified_at = Carbon::now();
                $tokenData->save();

                $user_data = [
                    'name' => $request->name,
                    'email' => $request->email,
                    "{$verify_type}_verified_at" => $tokenData->verified_at,
                    'phone' => $request->phone,
                    'password' => Hash::make($request->password),
                    'address' => $addresses
                ];
                if (isset($request->dob)) {
                    $user_data['dob'] = $request->dob;
                }
                if (isset($request->gender)) {
                    $user_data['gender'] = $request->gender;
                }
                if (isset($request->referral_code)) {
                    $user_data['referral_code'] = $request->referral_code;
                }

                $user = $this->repository->createUser($user_data, $roles);
                $payload['token'] = $user->createToken('auth_token')->plainTextToken;

                // clear email request attempts on successful register
                clearThrottleLimit("send-{$verify_type}-verify");
                // hit register limit when completed registration
                RateLimiter::hit('code-register' . $request->ip(), config('throttle.register.decay_minutes') * 60);

                $this->repository->replaceCart($user, $cart);

                $order = $payload['order'];
                $order->customer_id = $user->id;
                $order->save();

                if ($payload['payment']) {
                    $payment = $payload['payment'];
                    $payment->customer_id = $user->id;
                    $payment->save();
                }
            }
            if (isset($payload['applied_rule_ids'])) {
                $this->repository->createOrderDiscountUsage($payload['order'], $payload['applied_rule_ids']);
            }
            return $payload;
        }
        throw new \Exception(SOMETHING_WENT_WRONG);
    }

    public function update(Request $request, $id)
    {
        $request = app(OrderUpdateRequest::class);
        $request->id = $id;
        return $this->updateOrder($request);
    }

    public function updateOrder(Request $request)
    {
        try {
            $order = $this->repository->findOrFail($request->id);
        } catch (\Exception $e) {
            throw new \Exception(NOT_FOUND);
        }
        $this->changeOrderStatus($order, $request);
    }

    public function updateOrderAttributes(OrderUpdateAttributesRequest $request, $id)
    {
        try {
            $order = $this->repository->findOrFail($id);
            return $this->repository->saveAttributes($order, $request);
        } catch (\Exception $e) {
            throw new \Exception(NOT_FOUND);
        }
    }

    public function changeOrderStatus($order, $request)
    {
        parent::changeOrderStatus($order, $request['status']);
        $this->repository->createHistory($order, [
            'order_status_id' => $request['status'],
            'remarks' => $request['remarks'],
            'require_email_notification' => $request['require_email_notification'],
            'require_tng_notification' => isset($request['require_tng_notification']) ? $request['require_tng_notification'] : false
        ]);
        return $order;
    }

    public function payment(Request $request, $id, $action = "get")
    {
        try {
            $order = $this->repository->findOrFail($id);
        } catch (\Exception $e) {
            throw new \Exception(NOT_FOUND);
        }
        if (!$order->payment_method_id || !in_array($order->status()->first()->slug, [OrderEnum::PENDING, OrderEnum::CANCELED, OrderEnum::HOLDED])) {
            return ['payment_required' => false];
        }
        $gateway = $order->payment_method()->first()->gateway;

        $payment = $order->payments()->first();
        if (!$payment || !$payment->payment_reference_id) {
            $action = 'get';
        }

        switch ($gateway->code) {
            case 'ipay88':
            case 'malaysia':
                $response = $this->repository->generateIPay88PaymentPayload($order);
                break;

            case 'cod':
            case 'cashondelivery':
                $response = ['payment_required' => false];
                break;

            case PaymentEnum::TNG_GATEWAY_CODE:
                switch ($action) {
                    case 'get':
                        $response = $this->createTNGPayment($order);
                        break;
                    case 'status':
                    case 'cancel':
                        $status = $this->checkTNGPayment($order, $action == "cancel");
                        $response = [
                            'status' => $status
                        ];
                        break;
                }
                break;

            case PaymentEnum::RAZER_GATEWAY_CODE:
                switch ($action) {
                    case 'get':
                        $response = $this->repository->generateRazerPayPayment($order);
                        break;
                    case 'status':
                    case 'cancel':
                        $status = $this->checkRazerPayPayment($order);
                        $response = [
                            'status' => $status
                        ];
                        break;
                }
                break;
        }
        return $response;
    }

    public function invoice(Request $request, $id)
    {
        try {
            $order = $this->repository->findOrFail($id);
        } catch (\Exception $e) {
            throw new \Exception(NOT_FOUND);
        }

        if ($order->is_invoiced) {
            return true;
        }

        // create invoice
        $response = $this->repository->storeInvoice($order, $request);

        // update payment transaction to paid/completed/successful if payment status is pending
        $transaction = $order->payments()->first();
        if ($transaction && $transaction->status === "pending") {
            // update payment transaction status and additional info (TODO)
            $transaction->fill([
                // 'payment_reference_id' => $validatedData['TransId'] ?: null,
                // 'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $validatedData, ['Requery Response' => $requery_response]),
                'status' => 'success'
            ])->save();
            $this->repository->shipment($order);
        }

        return $response;
    }

    public function ship(OrderShipRequest $request, $id)
    {
        try {
            $order = $this->repository->findOrFail($id);
        } catch (\Exception $e) {
            throw new \Exception(NOT_FOUND);
        }

        // create shipment
        $response = $this->repository->storeShipment($order, $request);
        return $response;
    }

    public function refund(OrderRefundRequest $request, $id)
    {
        try {
            $order = $this->repository->findOrFail($id);
        } catch (\Exception $e) {
            throw new \Exception(NOT_FOUND);
        }

        $user = $request->user();
        if ($user && $user->hasRole(RoleEnum::SUPER_ADMIN)) {
            // create credit memo
            $response = $this->repository->storeCreditMemo($order, $request);
            return $response;
        }
        throw new \Exception(NOT_AUTHORIZED);
    }

    public function delyvaPrintLabel(Request $request, $id)
    {
        try {
            $order = $this->repository->findOrFail($id);
        } catch (\Exception $e) {
            throw new \Exception(NOT_FOUND);
        }

        return ['url' => $this->repository->getDelyvaPrintLabelUrl($order)];
    }

    public function delyvaPrintLabelsPerGroup(Request $request, $id)
    {
        try {
            $order = $this->repository->findOrFail($id);
        } catch (\Exception $e) {
            throw new \Exception(NOT_FOUND);
        }

        return ['groups' => $this->repository->getDelyvaPrintLabelUrlsPerGroup($order)];
    }

    public function delyvaPrintLabelBlob(Request $request, $id)
    {
        try {
            $order = $this->repository->findOrFail($id);
        } catch (\Exception $e) {
            throw new \Exception(NOT_FOUND);
        }
        $url = $this->repository->getDelyvaPrintLabelUrl($order);
        try {
            // Use Guzzle HTTP client to fetch the PDF
            $client = new \GuzzleHttp\Client();
            $response = $client->get($url);

            if ($response->getStatusCode() == 200) {
                // Get PDF content
                $pdfContent = $response->getBody()->getContents();

                // Return as binary response
                return response($pdfContent, 200)
                    ->header('Content-Type', 'application/pdf')
                    ->header('Content-Disposition', 'inline; filename="proxied-document.pdf"');
            } else {
                return response()->json(['error' => 'Failed to fetch PDF'], 500);
            }
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    protected function createTNGPayment(Order $order)
    {
        $payment_method = $order->payment_method()->first();
        $payment_gateway = $payment_method->gateway;
        $payment = $order->payments()->first();

        $responseData = $this->tngService->createTNGPayment(
            $payment->uuid,
            $order->total,
            $order->display_id,
            $payment_gateway->credentials['partner_id'],
            $payment_gateway->credentials['client_id']
        );
        if (isset($responseData['error'])) {
            throw new \Exception($responseData['error']);
        }
        if (
            isset($responseData['result']) &&
            isset($responseData['paymentId']) &&
            isset($responseData['actionForm']) &&
            in_array($responseData['result']['resultStatus'], ['S', 'A'])
        ) {
            $payment->payment_reference_id = $responseData['paymentId'];
            $payment->save();
            $payment_data = [
                'payload' => ['redirection_url' => $responseData['actionForm']['redirectionUrl']],
                'form_method' => "",
                'form_submit' => false,
                'payment_url' => $responseData['actionForm']['redirectionUrl'],
                'open_webview' => false,
                'payment_required' => true,
            ];
            return $payment_data;
        }
        throw new \Exception(isset($responseData['result']) ? $responseData['result']['resultMessage'] : "Failed to initiate payment");
    }

    public function frontendPaymentCallback(Request $request, $gateway_code)
    {
        switch ($gateway_code) {
            case 'ipay88':
            case 'malaysia':
                $this->processIPay88Payment($request, $gateway_code, true);
                break;

            case PaymentEnum::RAZER_GATEWAY_CODE:
                $this->processRazerPayPayment($request, $gateway_code, "return", true);
                break;
        }
        return Redirect::to(config('shop.frontend_payment_callback_url'));
    }

    public function paymentCallback(Request $request, $gateway_code, $action = "callback")
    {
        switch ($gateway_code) {
            case 'ipay88':
            case 'malaysia':
                $this->processIPay88Payment($request, $gateway_code);
                return 'RECEIVEOK';

            case PaymentEnum::TNG_GATEWAY_CODE:
                return $this->processTNGPayment($request, $gateway_code);

            case PaymentEnum::RAZER_GATEWAY_CODE:
                $result = $this->processRazerPayPayment($request, $gateway_code, $action);
                if ($action == 'callback') {
                    return 'CBTOKEN:MPSTATOK';
                }
                return $result;
        }
    }

    protected function processIPay88Payment($request, $gateway_code, $from_frontend = false)
    {
        $gateway = PaymentGateway::where('code', $gateway_code)->firstOrFail();
        $request = app(IPay88CallbackRequest::class);
        $validatedData = $request->validated();

        Log::info("IPay88Callback", [
            'data' => $validatedData,
            'frontend' => $from_frontend
        ]);

        // check order amount
        $order = $this->repository->find($validatedData['Remark']) ?: $this->repository->findOneByField('display_id', $validatedData['RefNo']);
        if (!$order) {
            throw new \Exception(NOT_FOUND);
        }
        $payment_method = $order->payment_method()->firstOrFail();
        if ($this->repository->ipayConvertAmountFormat($order->total) != $validatedData['Amount']) {
            throw new \Exception(ACTION_NOT_VALID);
        }

        $transaction = $order->payments()->first();
        if (!$transaction) {
            throw new \Exception("Transaction not exists");
        }

        if (!isset($validatedData['Signature'])) {
            $cancel_status = OrderStatus::firstWhere('slug', OrderEnum::CANCELED);
            $order->fill([
                'status' => $cancel_status ? $cancel_status->id : $order->status
            ])->save();
            $transaction->fill([
                'payment_reference_id' => $validatedData['TransId'] ?: null,
                'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $validatedData),
                'status' => 'failed'
            ])->save();

            $this->repository->createHistory($order, ['order_status_id' => $order->status]);
        } else {
            // verify signature
            if (
                !$this->repository->iPay88SignatureMatch(
                    $gateway->credentials['merchant_key'],
                    $validatedData['MerchantCode'],
                    $validatedData['PaymentId'],
                    $validatedData['RefNo'],
                    $validatedData['Amount'],
                    $validatedData['Currency'],
                    $validatedData['Status'],
                    $validatedData['Signature']
                )
            ) {
                throw new \Exception(ACTION_NOT_VALID);
            }

            $requery_response = $this->repository->requeryIPay88Payment($validatedData['MerchantCode'], $validatedData['RefNo'], $validatedData['Amount']);
            if ($payment_method->payment_info['payment_code'] != $validatedData['PaymentId']) {
                $payment_method = PaymentMethod::whereJsonContains('payment_info->payment_code', $validatedData['PaymentId'])->first();
            }
            if ($validatedData['Status'] == '1') {
                if ($requery_response == '00') {
                    // not to process success transaction again if it was success from previous callback
                    if ($transaction->status != PaymentTransactionEnum::SUCCESS && !$order->hasStatus(OrderEnum::PROCESSING)) {
                        // order success only when received success response from both callback and requery
                        $order_status = OrderStatus::firstWhere('slug', OrderEnum::PROCESSING);
                        $order->fill([
                            'status' => $order_status ? $order_status->id : $order->status + 1,
                            'paid_total' => $order->total,
                            'payment_method_id' => $payment_method ? $payment_method->id : $order->payment_method_id,
                            'payment_id' => $payment_method ? $payment_method->code : $order->payment_id,
                        ])->save();
                        $transaction->fill([
                            'payment_reference_id' => $validatedData['TransId'] ?: null,
                            'payment_method_id' => $payment_method ? $payment_method->id : $order->payment_method_id,
                            'payment_id' => $payment_method ? $payment_method->code : $transaction->payment_id,
                            'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $validatedData),
                            'status' => 'success'
                        ])->save();

                        $this->repository->createHistory($order, [
                            'order_status_id' => $order->status,
                            'require_email_notification' => true
                        ]);

                        OrderSuccess::dispatch($order);
                    }
                } else {
                    $order_status = OrderStatus::firstWhere('slug', OrderEnum::HOLDED);
                    $order->fill([
                        'status' => $order_status ? $order_status->id : $order->status,
                        'payment_method_id' => $payment_method ? $payment_method->id : $order->payment_method_id,
                        'payment_id' => $payment_method ? $payment_method->code : $order->payment_id,
                    ])->save();
                    $transaction->fill([
                        'payment_reference_id' => $validatedData['TransId'] ?: null,
                        'payment_method_id' => $payment_method ? $payment_method->id : $order->payment_method_id,
                        'payment_id' => $payment_method ? $payment_method->code : $transaction->payment_id,
                        'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $validatedData, ['Requery Response' => $requery_response]),
                        'status' => 'holded'
                    ])->save();

                    $this->repository->createHistory($order, [
                        'order_status_id' => $order->status,
                        'remarks' => 'Payment holded for backend check',
                        'require_email_notification' => true
                    ]);
                }
            } else if ($validatedData['Status'] == '0' || $requery_response == 'Payment fail') {
                $cancel_status = OrderStatus::firstWhere('slug', OrderEnum::CANCELED);
                $order->fill([
                    'status' => $cancel_status ? $cancel_status->id : $order->status,
                    'payment_method_id' => $payment_method ? $payment_method->id : $order->payment_method_id,
                    'payment_id' => $payment_method ? $payment_method->code : $order->payment_id,
                ])->save();
                $transaction->fill([
                    'payment_reference_id' => $validatedData['TransId'] ?: null,
                    'payment_method_id' => $payment_method ? $payment_method->id : $order->payment_method_id,
                    'payment_id' => $payment_method ? $payment_method->code : $transaction->payment_id,
                    'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $validatedData, ['Requery Response' => $requery_response]),
                    'status' => 'failed'
                ])->save();

                $this->repository->createHistory($order, ['order_status_id' => $order->status]);
            }
        }
    }

    protected function processTNGPayment($request, $gateway_code)
    {
        $gateway = PaymentGateway::where('code', $gateway_code)->firstOrFail();
        $request = app(TNGCallbackRequest::class);
        $validatedData = $request->validated();

        Log::info("TNGCallback", [
            'headers' => [
                'Client-Id' => $request->header('Client-Id'),
                'Request-Time' => $request->header('Request-Time'),
                'Signature' => $request->header('Signature'),
                'Trace-Id' => $request->header('Trace-Id')
            ],
            'json' => $validatedData
        ]);

        $client_id = $gateway->credentials['client_id'];
        $partner_id = $gateway->credentials['partner_id'];

        // check order amount
        $order = $this->repository->withoutBranch()->whereHas('payments', fn($query) => $query->where('uuid', $validatedData['paymentRequestId']))->first();
        if (!$order) {
            throw new \Exception(NOT_FOUND);
        }

        if ($validatedData['paymentResult']['resultStatus'] === 'S') {
            if ($this->repository->tngConvertAmountFormat($order->total) != $validatedData['paymentAmount']['value']) {
                throw new \Exception(ACTION_NOT_VALID);
            }
        }

        // check request signature
        $signatureMatched = $this->tngService->validateTNGSignature(
            'POST',
            config('tng.notify_payment_path'),
            $request->all(),
            [
                'Client-Id' => [$request->header('Client-Id')],
                'Request-Time' => [$request->header('Request-Time')],
                'Signature' => [$request->header('Signature')]
            ],
            'Request-Time',
            $client_id,
            $this->tngService->getPublicKey()
        );
        if (!$signatureMatched) {
            throw new \Exception('Signature not match');
        }

        $transaction = $order->payments()->firstWhere([
            'uuid' => $validatedData['paymentRequestId'],
            'payment_reference_id' => $validatedData['paymentId'],
            'status' => 'pending'
        ]);
        if ($transaction && $order->hasStatus(OrderEnum::PENDING)) {
            $requery_response = $this->tngService->inquiryTNGPayment($transaction->uuid, $transaction->payment_reference_id, $partner_id, $client_id);
            if ($validatedData['paymentResult']['resultStatus'] === 'S') {
                if ($requery_response['paymentStatus'] == 'SUCCESS') {
                    // order success only when received success response from both callback and requery
                    $order_status = OrderStatus::firstWhere('slug', OrderEnum::PROCESSING);
                    $order->fill([
                        'status' => $order_status ? $order_status->id : $order->status + 1,
                        'paid_total' => $order->total,
                    ])->save();
                    $transaction->fill([
                        'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $validatedData),
                        'status' => 'success'
                    ])->save();

                    $this->repository->createHistory($order, [
                        'order_status_id' => $order->status,
                        'require_email_notification' => true
                    ]);

                    OrderSuccess::dispatch($order);
                } else {
                    $order_status = OrderStatus::firstWhere('slug', OrderEnum::HOLDED);
                    $order->fill([
                        'status' => $order_status ? $order_status->id : $order->status,
                    ])->save();
                    $transaction->fill([
                        'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $validatedData, ['Requery Response' => $requery_response]),
                        'status' => 'holded'
                    ])->save();

                    $this->repository->createHistory($order, [
                        'order_status_id' => $order->status,
                        'remarks' => 'Payment holded for backend check',
                        'require_email_notification' => true
                    ]);
                }
            } else {
                $cancel_status = OrderStatus::firstWhere('slug', OrderEnum::CANCELED);
                $order->fill([
                    'status' => $cancel_status ? $cancel_status->id : $order->status,
                ])->save();
                $transaction->fill([
                    'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $validatedData, ['Requery Response' => $requery_response, 'Failed Reason' => $request->paymentFailReason]),
                    'status' => 'failed'
                ])->save();

                $this->repository->createHistory($order, ['order_status_id' => $order->status]);
            }
        }

        $data = [
            'result' => [
                'resultCode' => 'SUCCESS',
                'resultStatus' => 'S',
                'resultMessage' => 'success'
            ]
        ];
        return response($data)
            ->withHeaders($this->tngService->prepareHeaders(
                'POST',
                config('tng.notify_payment_path'),
                $data,
                'Response-Time',
                $client_id,
                $this->tngService->getPrivateKey()
            ));
    }

    protected function processRazerPayPayment($request, $gateway_code, $notify_type, $from_frontend = false)
    {
        $gateway = PaymentGateway::where('code', $gateway_code)->firstOrFail();
        $request = app(RazerPayCallbackRequest::class);
        $validatedData = $request->validated();

        Log::info("RazerPayCallback", [
            'data' => $validatedData,
            'frontend' => $from_frontend
        ]);

        if ($notify_type != 'callback') {
            $this->repository->returnRazerPayIPN($request->all(), $notify_type);
        }

        // check order amount
        $order = $this->repository->firstWhere('display_id', $validatedData['orderid']);
        if (!$order) {
            throw new \Exception(NOT_FOUND);
        }

        $transaction = $order->payments()->firstWhere('payment_reference_id', $validatedData['tranID']);
        if (!$transaction) {
            throw new \Exception("Transaction not exists");
        }

        // check request skey
        $signatureMatched = $this->repository->razerPaySKeyMatch(
            $validatedData['tranID'],
            $validatedData['orderid'],
            $validatedData['status'],
            $validatedData['domain'],
            $validatedData['amount'],
            $validatedData['currency'],
            $validatedData['paydate'],
            $validatedData['appcode'],
            $gateway->credentials['secret_key'],
            $validatedData['skey'],
        );
        if (!$signatureMatched) {
            throw new \Exception('Signature not match');
        }

        $payment_method = $order->payment_method()->firstOrFail();

        $verify_key = $gateway->credentials['verify_key'];
        $secret_key = $gateway->credentials['secret_key'];
        $merchant_id = $gateway->credentials['merchant_id'];

        $status = 'pending';
        $requery_response = $this->repository->inquiryRazerPayPayment($transaction->amount, $transaction->payment_reference_id, $merchant_id, $verify_key, $secret_key);
        if ($payment_method->payment_info['payment_code'] != $validatedData['channel']) {
            $payment_method = PaymentMethod::whereJsonContains('payment_info->payment_code', $validatedData['channel'])->first() ?: $payment_method;
        }
        if ($validatedData['status'] == '00') {
            if (isset($requery_response['StatCode']) && $requery_response['StatCode'] == '00') {
                // not to process success transaction again if it was success from previous callback
                if ($transaction->status != PaymentTransactionEnum::SUCCESS && !$order->hasStatus(OrderEnum::PROCESSING)) {
                    // order success only when received success response from both callback and requery
                    $order_status = OrderStatus::firstWhere('slug', OrderEnum::PROCESSING);
                    $order->fill([
                        'status' => $order_status ? $order_status->id : $order->status + 1,
                        'paid_total' => $order->total,
                        'payment_method_id' => $payment_method ? $payment_method->id : $order->payment_method_id,
                        'payment_id' => $payment_method ? $payment_method->code : $order->payment_id,
                    ])->save();
                    $transaction->fill([
                        'payment_method_id' => $payment_method ? $payment_method->id : $order->payment_method_id,
                        'payment_id' => $payment_method ? $payment_method->code : $transaction->payment_id,
                        'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $validatedData),
                        'status' => 'success'
                    ])->save();

                    $this->repository->createHistory($order, [
                        'order_status_id' => $order->status,
                        'require_email_notification' => true
                    ]);

                    OrderSuccess::dispatch($order);
                }
                $status = 'success';
            } else {
                $order_status = OrderStatus::firstWhere('slug', OrderEnum::HOLDED);
                $order->fill([
                    'status' => $order_status ? $order_status->id : $order->status,
                    'payment_method_id' => $payment_method ? $payment_method->id : $order->payment_method_id,
                    'payment_id' => $payment_method ? $payment_method->code : $order->payment_id,
                ])->save();
                $transaction->fill([
                    'payment_method_id' => $payment_method ? $payment_method->id : $order->payment_method_id,
                    'payment_id' => $payment_method ? $payment_method->code : $transaction->payment_id,
                    'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $validatedData, ['Requery Response' => $requery_response]),
                    'status' => 'holded'
                ])->save();

                $this->repository->createHistory($order, [
                    'order_status_id' => $order->status,
                    'remarks' => 'Payment holded for backend check',
                    'require_email_notification' => true
                ]);
            }
        } else if ($validatedData['status'] == '11' || (isset($requery_response['StatCode']) && $requery_response['StatCode'] == '11')) {
            $cancel_status = OrderStatus::firstWhere('slug', OrderEnum::CANCELED);
            $order->fill([
                'status' => $cancel_status ? $cancel_status->id : $order->status,
                'payment_method_id' => $payment_method ? $payment_method->id : $order->payment_method_id,
                'payment_id' => $payment_method ? $payment_method->code : $order->payment_id,
            ])->save();
            $transaction->fill([
                'payment_method_id' => $payment_method ? $payment_method->id : $order->payment_method_id,
                'payment_id' => $payment_method ? $payment_method->code : $transaction->payment_id,
                'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $validatedData, ['Requery Response' => $requery_response]),
                'status' => 'failed'
            ])->save();

            $this->repository->createHistory($order, ['order_status_id' => $order->status]);

            $status = 'failed';
        }

        return $status;
    }

    protected function checkTNGPayment(Order $order, $is_remove_when_pending = false)
    {
        $payment_method = $order->payment_method()->first();
        $gateway = $payment_method->gateway;

        $client_id = $gateway->credentials['client_id'];
        $partner_id = $gateway->credentials['partner_id'];

        $status = 'pending';
        $transaction = $order->payments()->first();
        if ($transaction && $transaction->status == 'pending' && $order->hasStatus(OrderEnum::PENDING)) {
            $requery_response = $this->tngService->inquiryTNGPayment($transaction->uuid, $transaction->payment_reference_id, $partner_id, $client_id);
            if ($requery_response['paymentStatus'] == 'SUCCESS') {
                // order success only when received success response from both callback and requery
                $order_status = OrderStatus::firstWhere('slug', OrderEnum::PROCESSING);
                $order->fill([
                    'status' => $order_status ? $order_status->id : $order->status + 1,
                    'paid_total' => $order->total,
                ])->save();
                $transaction->fill([
                    'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $requery_response),
                    'status' => 'success'
                ])->save();

                $this->repository->createHistory($order, [
                    'order_status_id' => $order->status,
                    'require_email_notification' => true
                ]);

                OrderSuccess::dispatch($order);

                $status = 'success';
            } else if (in_array($requery_response['paymentStatus'], ['FAIL', 'CANCELLED']) || $is_remove_when_pending) {
                $cancel_status = OrderStatus::firstWhere('slug', OrderEnum::CANCELED);
                $order->fill([
                    'status' => $cancel_status ? $cancel_status->id : $order->status,
                ])->save();
                $transaction->fill([
                    'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $requery_response),
                    'status' => 'failed'
                ])->save();

                $this->repository->createHistory($order, ['order_status_id' => $order->status]);

                $status = 'failed';
            }
        } else {
            $status = $transaction->status;
        }

        return $status;
    }

    protected function checkRazerPayPayment(Order $order)
    {
        $payment_method = $order->payment_method()->first();
        $gateway = $payment_method->gateway;

        $verify_key = $gateway->credentials['verify_key'];
        $secret_key = $gateway->credentials['secret_key'];
        $merchant_id = $gateway->credentials['merchant_id'];

        $status = 'pending';
        $transaction = $order->payments()->first();
        if ($transaction && $transaction->status == 'pending' && $order->hasStatus(OrderEnum::PENDING)) {
            $requery_response = $this->repository->inquiryRazerPayPayment($transaction->amount, $transaction->payment_reference_id, $merchant_id, $verify_key, $secret_key);
            if ($requery_response['StatCode'] == '00') {
                // order success only when received success response from both callback and requery
                $order_status = OrderStatus::firstWhere('slug', OrderEnum::PROCESSING);
                $order->fill([
                    'status' => $order_status ? $order_status->id : $order->status + 1,
                    'paid_total' => $order->total,
                ])->save();
                $transaction->fill([
                    'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $requery_response),
                    'status' => 'success'
                ])->save();

                $this->repository->createHistory($order, [
                    'order_status_id' => $order->status,
                    'require_email_notification' => true
                ]);

                OrderSuccess::dispatch($order);

                $status = 'success';
            } else if ($requery_response['StatCode'] == '11') {
                $cancel_status = OrderStatus::firstWhere('slug', OrderEnum::CANCELED);
                $order->fill([
                    'status' => $cancel_status ? $cancel_status->id : $order->status,
                ])->save();
                $transaction->fill([
                    'response_message' => $this->repository->generateTransactionResponseMessage($gateway, $requery_response),
                    'status' => 'failed'
                ])->save();

                $this->repository->createHistory($order, ['order_status_id' => $order->status]);

                $status = 'failed';
            }
        } else {
            $status = $transaction->status;
        }

        return $status;
    }

    /**
     * Proxy to fetch external PDF and return as binary response
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function proxyPdf(Request $request)
    {
        $validatedData = $request->validate([
            'url' => 'required|url',
        ]);

        $externalUrl = $validatedData['url'];

        try {
            // Use Guzzle HTTP client to fetch the PDF
            $client = new \GuzzleHttp\Client();
            $response = $client->get($externalUrl);

            if ($response->getStatusCode() == 200) {
                // Get PDF content
                $pdfContent = $response->getBody()->getContents();

                // Return as binary response
                return response($pdfContent, 200)
                    ->header('Content-Type', 'application/pdf')
                    ->header('Content-Disposition', 'inline; filename="proxied-document.pdf"');
            } else {
                return response()->json(['error' => 'Failed to fetch PDF'], 500);
            }
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Bulk update order status
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bulkUpdateStatus(Request $request)
    {
        $validated = $request->validate([
            'order_ids' => 'required|array',
            'order_ids.*' => 'required|numeric',
            'status' => 'required|exists:order_status,id',
            'remarks' => 'nullable|string',
            'require_email_notification' => 'nullable|boolean',
        ]);

        $results = [];
        $successCount = 0;
        $failedCount = 0;

        foreach ($validated['order_ids'] as $orderId) {
            try {
                $order = $this->repository->findOrFail($orderId);
                $this->changeOrderStatus($order, [
                    'status' => $validated['status'],
                    'remarks' => $validated['remarks'] ?? '',
                    'require_email_notification' => $validated['require_email_notification'] ?? false,
                ]);
                $results[$orderId] = true;
                $successCount++;
            } catch (\Exception $e) {
                $results[$orderId] = false;
                $failedCount++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Updated {$successCount} orders successfully. Failed: {$failedCount}",
            'results' => $results
        ]);
    }

    /**
     * Get selected orders by IDs
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSelectedOrders(Request $request)
    {
        $validated = $request->validate([
            'ids' => 'required|string',
        ]);

        try {
            $orderIds = explode(',', $validated['ids']);
            $orders = $this->repository->with([
                'customer',
                'status',
                'payments',
                'payment_method'
            ])->whereIn('id', $orderIds)->get();

            return response()->json($orders);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
